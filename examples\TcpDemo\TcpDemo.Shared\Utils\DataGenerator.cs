using System.Text;
using TcpDemo.Shared.Models;

namespace TcpDemo.Shared.Utils;

/// <summary>
/// 测试数据生成器
/// </summary>
public static class DataGenerator
{
    private static readonly Random _random = new();
    private static readonly string[] _sampleTexts = {
        "Hello, World!",
        "TCP通信测试消息",
        "Performance test data",
        "网络连接质量检测",
        "Large data transfer test",
        "并发连接压力测试",
        "心跳检测机制验证",
        "SSL/TLS安全连接测试",
        "自动重连功能验证",
        "数据完整性校验"
    };

    private static readonly string[] _commands = {
        "ping",
        "status",
        "info",
        "stats",
        "help",
        "version",
        "config",
        "reset",
        "test",
        "benchmark"
    };

    /// <summary>
    /// 生成随机文本消息
    /// </summary>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>文本消息</returns>
    public static DemoMessage GenerateTextMessage(string? senderId = null, string? receiverId = null)
    {
        var text = _sampleTexts[_random.Next(_sampleTexts.Length)];
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
        var content = $"{text} - {timestamp}";

        return DemoMessage.CreateTextMessage(content, senderId, receiverId);
    }

    /// <summary>
    /// 生成随机命令消息
    /// </summary>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>命令消息</returns>
    public static DemoMessage GenerateCommandMessage(string? senderId = null, string? receiverId = null)
    {
        var command = _commands[_random.Next(_commands.Length)];
        var parameters = _random.Next(2) == 0 ? "" : $" param{_random.Next(1, 4)}";
        var content = command + parameters;

        return DemoMessage.CreateCommandMessage(content, senderId, receiverId);
    }

    /// <summary>
    /// 生成指定大小的随机数据
    /// </summary>
    /// <param name="sizeBytes">数据大小（字节）</param>
    /// <returns>随机数据</returns>
    public static byte[] GenerateRandomData(int sizeBytes)
    {
        var data = new byte[sizeBytes];
        _random.NextBytes(data);
        return data;
    }

    /// <summary>
    /// 生成指定大小的文本数据
    /// </summary>
    /// <param name="sizeBytes">数据大小（字节）</param>
    /// <returns>文本数据</returns>
    public static string GenerateRandomText(int sizeBytes)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 ";
        var result = new StringBuilder();
        
        while (Encoding.UTF8.GetByteCount(result.ToString()) < sizeBytes)
        {
            result.Append(chars[_random.Next(chars.Length)]);
        }

        // 确保精确的字节大小
        var text = result.ToString();
        while (Encoding.UTF8.GetByteCount(text) > sizeBytes)
        {
            text = text[..^1];
        }

        return text;
    }

    /// <summary>
    /// 生成大数据消息
    /// </summary>
    /// <param name="sizeBytes">消息大小（字节）</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>大数据消息</returns>
    public static DemoMessage GenerateLargeDataMessage(int sizeBytes, string? senderId = null, string? receiverId = null)
    {
        var message = new DemoMessage
        {
            Type = DemoMessageType.FileTransfer,
            SenderId = senderId,
            ReceiverId = receiverId,
            Content = $"Large data transfer - {sizeBytes} bytes",
            Data = GenerateRandomData(sizeBytes)
        };

        return message;
    }

    /// <summary>
    /// 生成性能测试消息
    /// </summary>
    /// <param name="sequenceNumber">序列号</param>
    /// <param name="sizeBytes">消息大小（字节）</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>性能测试消息</returns>
    public static DemoMessage GeneratePerformanceTestMessage(int sequenceNumber, int sizeBytes, string? senderId = null, string? receiverId = null)
    {
        var baseContent = $"Performance test message #{sequenceNumber} - {DateTime.UtcNow:HH:mm:ss.fff}";
        var padding = GenerateRandomText(Math.Max(0, sizeBytes - Encoding.UTF8.GetByteCount(baseContent) - 100));
        var content = baseContent + " " + padding;

        var message = new DemoMessage
        {
            Type = DemoMessageType.PerformanceTest,
            SenderId = senderId,
            ReceiverId = receiverId,
            Content = content,
            Properties = new Dictionary<string, object>
            {
                ["SequenceNumber"] = sequenceNumber,
                ["ExpectedSize"] = sizeBytes,
                ["GeneratedAt"] = DateTime.UtcNow
            }
        };

        return message;
    }

    /// <summary>
    /// 生成消息序列
    /// </summary>
    /// <param name="count">消息数量</param>
    /// <param name="messageSize">消息大小（字节）</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>消息序列</returns>
    public static IEnumerable<DemoMessage> GenerateMessageSequence(int count, int messageSize, string? senderId = null, string? receiverId = null)
    {
        for (int i = 0; i < count; i++)
        {
            yield return GeneratePerformanceTestMessage(i + 1, messageSize, senderId, receiverId);
        }
    }

    /// <summary>
    /// 生成测试场景数据
    /// </summary>
    /// <param name="scenario">测试场景</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>测试消息序列</returns>
    public static IEnumerable<DemoMessage> GenerateTestData(TestScenario scenario, string? senderId = null, string? receiverId = null)
    {
        switch (scenario.Type)
        {
            case TestScenarioType.BasicConnection:
                yield return DemoMessage.CreateTextMessage("Connection test message", senderId, receiverId);
                break;

            case TestScenarioType.MessageExchange:
                for (int i = 0; i < scenario.MessageCount; i++)
                {
                    yield return GenerateTextMessage(senderId, receiverId);
                }
                break;

            case TestScenarioType.HeartbeatTest:
                for (int i = 0; i < 10; i++)
                {
                    yield return DemoMessage.CreateHeartbeatMessage(senderId, receiverId);
                }
                break;

            case TestScenarioType.LargeDataTransferTest:
                for (int i = 0; i < scenario.MessageCount; i++)
                {
                    yield return GenerateLargeDataMessage(scenario.MessageSize, senderId, receiverId);
                }
                break;

            case TestScenarioType.PerformanceStressTest:
                for (int i = 0; i < scenario.MessageCount; i++)
                {
                    yield return GeneratePerformanceTestMessage(i + 1, scenario.MessageSize, senderId, receiverId);
                }
                break;

            case TestScenarioType.ConcurrentConnectionTest:
                for (int i = 0; i < scenario.MessageCount; i++)
                {
                    yield return GenerateTextMessage(senderId, receiverId);
                }
                break;

            default:
                yield return GenerateTextMessage(senderId, receiverId);
                break;
        }
    }

    /// <summary>
    /// 生成随机客户端ID
    /// </summary>
    /// <returns>客户端ID</returns>
    public static string GenerateClientId()
    {
        return $"Client_{_random.Next(1000, 9999)}";
    }

    /// <summary>
    /// 生成随机服务器ID
    /// </summary>
    /// <returns>服务器ID</returns>
    public static string GenerateServerId()
    {
        return $"Server_{Environment.MachineName}_{_random.Next(100, 999)}";
    }

    /// <summary>
    /// 计算数据校验和
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>校验和</returns>
    public static uint CalculateChecksum(byte[] data)
    {
        uint checksum = 0;
        for (int i = 0; i < data.Length; i++)
        {
            checksum += data[i];
        }
        return checksum;
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <param name="originalData">原始数据</param>
    /// <param name="receivedData">接收数据</param>
    /// <returns>是否完整</returns>
    public static bool ValidateDataIntegrity(byte[] originalData, byte[] receivedData)
    {
        if (originalData.Length != receivedData.Length)
            return false;

        return CalculateChecksum(originalData) == CalculateChecksum(receivedData);
    }

    /// <summary>
    /// 生成测试文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="sizeBytes">文件大小（字节）</param>
    public static void GenerateTestFile(string filePath, int sizeBytes)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
        var buffer = new byte[8192];
        var remaining = sizeBytes;

        while (remaining > 0)
        {
            var chunkSize = Math.Min(buffer.Length, remaining);
            _random.NextBytes(buffer.AsSpan(0, chunkSize));
            fileStream.Write(buffer, 0, chunkSize);
            remaining -= chunkSize;
        }
    }
}
