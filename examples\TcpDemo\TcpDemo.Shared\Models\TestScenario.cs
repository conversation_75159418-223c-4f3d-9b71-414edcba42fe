namespace TcpDemo.Shared.Models;

/// <summary>
/// 测试场景类型
/// </summary>
public enum TestScenarioType
{
    /// <summary>
    /// 基本连接测试
    /// </summary>
    BasicConnection,

    /// <summary>
    /// 消息发送接收测试
    /// </summary>
    MessageExchange,

    /// <summary>
    /// 心跳检测测试
    /// </summary>
    HeartbeatTest,

    /// <summary>
    /// 自动重连测试
    /// </summary>
    AutoReconnectTest,

    /// <summary>
    /// 并发连接测试
    /// </summary>
    ConcurrentConnectionTest,

    /// <summary>
    /// 大数据传输测试
    /// </summary>
    LargeDataTransferTest,

    /// <summary>
    /// 长连接稳定性测试
    /// </summary>
    LongConnectionStabilityTest,

    /// <summary>
    /// SSL/TLS安全连接测试
    /// </summary>
    SslTlsTest,

    /// <summary>
    /// 性能压力测试
    /// </summary>
    PerformanceStressTest,

    /// <summary>
    /// 连接质量监控测试
    /// </summary>
    ConnectionQualityTest
}

/// <summary>
/// 测试场景配置
/// </summary>
public class TestScenario
{
    /// <summary>
    /// 场景ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 场景名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 场景类型
    /// </summary>
    public TestScenarioType Type { get; set; }

    /// <summary>
    /// 场景描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// 并发数量
    /// </summary>
    public int ConcurrencyLevel { get; set; } = 1;

    /// <summary>
    /// 测试持续时间（秒）
    /// </summary>
    public int DurationSeconds { get; set; } = 60;

    /// <summary>
    /// 消息数量
    /// </summary>
    public int MessageCount { get; set; } = 100;

    /// <summary>
    /// 消息大小（字节）
    /// </summary>
    public int MessageSize { get; set; } = 1024;

    /// <summary>
    /// 发送间隔（毫秒）
    /// </summary>
    public int SendIntervalMs { get; set; } = 100;

    /// <summary>
    /// 扩展参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 预期结果
    /// </summary>
    public Dictionary<string, object> ExpectedResults { get; set; } = new();

    /// <summary>
    /// 创建基本连接测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreateBasicConnectionTest()
    {
        return new TestScenario
        {
            Name = "基本连接测试",
            Type = TestScenarioType.BasicConnection,
            Description = "测试TCP客户端与服务器的基本连接建立和断开功能",
            TimeoutSeconds = 10,
            ExpectedResults = new Dictionary<string, object>
            {
                ["ConnectionSuccess"] = true,
                ["DisconnectionSuccess"] = true
            }
        };
    }

    /// <summary>
    /// 创建消息交换测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreateMessageExchangeTest()
    {
        return new TestScenario
        {
            Name = "消息发送接收测试",
            Type = TestScenarioType.MessageExchange,
            Description = "测试客户端与服务器之间的消息发送和接收功能",
            MessageCount = 50,
            MessageSize = 512,
            SendIntervalMs = 50,
            ExpectedResults = new Dictionary<string, object>
            {
                ["MessagesSent"] = 50,
                ["MessagesReceived"] = 50,
                ["SuccessRate"] = 1.0
            }
        };
    }

    /// <summary>
    /// 创建并发连接测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreateConcurrentConnectionTest()
    {
        return new TestScenario
        {
            Name = "并发连接测试",
            Type = TestScenarioType.ConcurrentConnectionTest,
            Description = "测试服务器处理多个并发客户端连接的能力",
            ConcurrencyLevel = 10,
            MessageCount = 20,
            DurationSeconds = 30,
            ExpectedResults = new Dictionary<string, object>
            {
                ["ConcurrentConnections"] = 10,
                ["TotalMessages"] = 200,
                ["ErrorRate"] = 0.0
            }
        };
    }

    /// <summary>
    /// 创建大数据传输测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreateLargeDataTransferTest()
    {
        return new TestScenario
        {
            Name = "大数据传输测试",
            Type = TestScenarioType.LargeDataTransferTest,
            Description = "测试大数据量的传输能力和稳定性",
            MessageCount = 10,
            MessageSize = 1024 * 1024, // 1MB
            TimeoutSeconds = 60,
            ExpectedResults = new Dictionary<string, object>
            {
                ["DataIntegrity"] = true,
                ["TransferSuccess"] = true,
                ["AverageSpeed"] = 10.0 // MB/s
            }
        };
    }

    /// <summary>
    /// 创建心跳检测测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreateHeartbeatTest()
    {
        return new TestScenario
        {
            Name = "心跳检测测试",
            Type = TestScenarioType.HeartbeatTest,
            Description = "测试心跳检测机制的正常工作",
            DurationSeconds = 60,
            Parameters = new Dictionary<string, object>
            {
                ["HeartbeatInterval"] = 5, // 5秒
                ["HeartbeatTimeout"] = 15  // 15秒
            },
            ExpectedResults = new Dictionary<string, object>
            {
                ["HeartbeatsSent"] = 12,
                ["HeartbeatsReceived"] = 12,
                ["ConnectionStable"] = true
            }
        };
    }

    /// <summary>
    /// 创建性能压力测试场景
    /// </summary>
    /// <returns>测试场景</returns>
    public static TestScenario CreatePerformanceStressTest()
    {
        return new TestScenario
        {
            Name = "性能压力测试",
            Type = TestScenarioType.PerformanceStressTest,
            Description = "测试系统在高负载下的性能表现",
            ConcurrencyLevel = 50,
            MessageCount = 1000,
            MessageSize = 1024,
            SendIntervalMs = 10,
            DurationSeconds = 120,
            ExpectedResults = new Dictionary<string, object>
            {
                ["ThroughputMsgPerSec"] = 500.0,
                ["AverageLatencyMs"] = 50.0,
                ["ErrorRate"] = 0.01,
                ["MemoryUsageMB"] = 100.0
            }
        };
    }

    /// <summary>
    /// 获取场景摘要
    /// </summary>
    /// <returns>场景摘要</returns>
    public string GetSummary()
    {
        return $"[{Type}] {Name} - 并发:{ConcurrencyLevel}, 消息:{MessageCount}, 持续:{DurationSeconds}s";
    }

    /// <summary>
    /// 验证场景配置
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, string ErrorMessage) Validate()
    {
        if (string.IsNullOrWhiteSpace(Name))
            return (false, "场景名称不能为空");

        if (TimeoutSeconds <= 0)
            return (false, "超时时间必须大于0");

        if (ConcurrencyLevel <= 0)
            return (false, "并发数量必须大于0");

        if (MessageCount < 0)
            return (false, "消息数量不能为负数");

        if (MessageSize <= 0)
            return (false, "消息大小必须大于0");

        if (DurationSeconds <= 0)
            return (false, "持续时间必须大于0");

        return (true, string.Empty);
    }
}
