using System.Text.Json;

namespace TcpDemo.Shared.Models;

/// <summary>
/// 测试结果状态
/// </summary>
public enum TestResultStatus
{
    /// <summary>
    /// 未开始
    /// </summary>
    NotStarted,

    /// <summary>
    /// 运行中
    /// </summary>
    Running,

    /// <summary>
    /// 成功
    /// </summary>
    Success,

    /// <summary>
    /// 失败
    /// </summary>
    Failed,

    /// <summary>
    /// 超时
    /// </summary>
    Timeout,

    /// <summary>
    /// 取消
    /// </summary>
    Cancelled,

    /// <summary>
    /// 部分成功
    /// </summary>
    PartialSuccess
}

/// <summary>
/// 测试结果
/// </summary>
public class TestResult
{
    /// <summary>
    /// 结果ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 场景ID
    /// </summary>
    public string ScenarioId { get; set; } = string.Empty;

    /// <summary>
    /// 场景名称
    /// </summary>
    public string ScenarioName { get; set; } = string.Empty;

    /// <summary>
    /// 测试状态
    /// </summary>
    public TestResultStatus Status { get; set; } = TestResultStatus.NotStarted;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 执行时长
    /// </summary>
    public TimeSpan? Duration => EndTime - StartTime;

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// 错误率
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// 成功操作数
    /// </summary>
    public long SuccessfulOperations { get; set; }

    /// <summary>
    /// 失败操作数
    /// </summary>
    public long FailedOperations { get; set; }

    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double AverageLatencyMs { get; set; }

    /// <summary>
    /// 最小延迟（毫秒）
    /// </summary>
    public double MinLatencyMs { get; set; }

    /// <summary>
    /// 最大延迟（毫秒）
    /// </summary>
    public double MaxLatencyMs { get; set; }

    /// <summary>
    /// 吞吐量（操作/秒）
    /// </summary>
    public double ThroughputOpsPerSec { get; set; }

    /// <summary>
    /// 数据传输量（字节）
    /// </summary>
    public long TotalBytesTransferred { get; set; }

    /// <summary>
    /// 传输速度（字节/秒）
    /// </summary>
    public double TransferRateBytesPerSec { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 详细指标
    /// </summary>
    public Dictionary<string, object> Metrics { get; set; } = new();

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 添加错误
    /// </summary>
    /// <param name="error">错误信息</param>
    public void AddError(string error)
    {
        Errors.Add($"[{DateTime.Now:HH:mm:ss}] {error}");
    }

    /// <summary>
    /// 添加警告
    /// </summary>
    /// <param name="warning">警告信息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add($"[{DateTime.Now:HH:mm:ss}] {warning}");
    }

    /// <summary>
    /// 添加指标
    /// </summary>
    /// <param name="name">指标名称</param>
    /// <param name="value">指标值</param>
    public void AddMetric(string name, object value)
    {
        Metrics[name] = value;
    }

    /// <summary>
    /// 开始测试
    /// </summary>
    public void Start()
    {
        Status = TestResultStatus.Running;
        StartTime = DateTime.UtcNow;
    }

    /// <summary>
    /// 完成测试
    /// </summary>
    /// <param name="status">最终状态</param>
    public void Complete(TestResultStatus status = TestResultStatus.Success)
    {
        Status = status;
        EndTime = DateTime.UtcNow;
        CalculateMetrics();
    }

    /// <summary>
    /// 计算指标
    /// </summary>
    private void CalculateMetrics()
    {
        if (TotalOperations > 0)
        {
            SuccessRate = (double)SuccessfulOperations / TotalOperations;
            ErrorRate = (double)FailedOperations / TotalOperations;
        }

        if (Duration.HasValue && Duration.Value.TotalSeconds > 0)
        {
            ThroughputOpsPerSec = TotalOperations / Duration.Value.TotalSeconds;
            TransferRateBytesPerSec = TotalBytesTransferred / Duration.Value.TotalSeconds;
        }
    }

    /// <summary>
    /// 获取结果摘要
    /// </summary>
    /// <returns>结果摘要</returns>
    public string GetSummary()
    {
        var duration = Duration?.ToString(@"mm\:ss") ?? "未知";
        return $"[{Status}] {ScenarioName} - 耗时:{duration}, 成功率:{SuccessRate:P2}, 吞吐量:{ThroughputOpsPerSec:F1}ops/s";
    }

    /// <summary>
    /// 获取详细报告
    /// </summary>
    /// <returns>详细报告</returns>
    public string GetDetailedReport()
    {
        var report = new List<string>
        {
            $"=== 测试结果报告 ===",
            $"场景名称: {ScenarioName}",
            $"测试状态: {Status}",
            $"开始时间: {StartTime:yyyy-MM-dd HH:mm:ss}",
            $"结束时间: {EndTime:yyyy-MM-dd HH:mm:ss}",
            $"执行时长: {Duration?.ToString(@"hh\:mm\:ss") ?? "未知"}",
            "",
            $"=== 性能指标 ===",
            $"总操作数: {TotalOperations:N0}",
            $"成功操作数: {SuccessfulOperations:N0}",
            $"失败操作数: {FailedOperations:N0}",
            $"成功率: {SuccessRate:P2}",
            $"错误率: {ErrorRate:P2}",
            $"平均延迟: {AverageLatencyMs:F2} ms",
            $"最小延迟: {MinLatencyMs:F2} ms",
            $"最大延迟: {MaxLatencyMs:F2} ms",
            $"吞吐量: {ThroughputOpsPerSec:F1} ops/s",
            $"数据传输量: {TotalBytesTransferred:N0} bytes ({TotalBytesTransferred / 1024.0 / 1024.0:F2} MB)",
            $"传输速度: {TransferRateBytesPerSec / 1024.0 / 1024.0:F2} MB/s",
            $"内存使用: {MemoryUsageBytes / 1024.0 / 1024.0:F2} MB",
            $"CPU使用率: {CpuUsagePercent:F1}%"
        };

        if (Metrics.Count > 0)
        {
            report.Add("");
            report.Add("=== 详细指标 ===");
            foreach (var metric in Metrics)
            {
                report.Add($"{metric.Key}: {metric.Value}");
            }
        }

        if (Errors.Count > 0)
        {
            report.Add("");
            report.Add("=== 错误信息 ===");
            report.AddRange(Errors);
        }

        if (Warnings.Count > 0)
        {
            report.Add("");
            report.Add("=== 警告信息 ===");
            report.AddRange(Warnings);
        }

        return string.Join(Environment.NewLine, report);
    }

    /// <summary>
    /// 序列化为JSON
    /// </summary>
    /// <returns>JSON字符串</returns>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });
    }

    /// <summary>
    /// 从JSON反序列化
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <returns>测试结果对象</returns>
    public static TestResult? FromJson(string json)
    {
        try
        {
            return JsonSerializer.Deserialize<TestResult>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch
        {
            return null;
        }
    }
}
