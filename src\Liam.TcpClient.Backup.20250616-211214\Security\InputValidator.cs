using System.Text;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Models;
using Liam.TcpClient.Constants;

namespace Liam.TcpClient.Security;

/// <summary>
/// 输入数据验证器
/// </summary>
public static class InputValidator
{
    private static readonly Regex _maliciousPatterns = new(
        @"(\x00|\x01|\x02|\x03|\x04|\x05|\x06|\x07|\x08|\x0B|\x0C|\x0E|\x0F|" +
        @"\x10|\x11|\x12|\x13|\x14|\x15|\x16|\x17|\x18|\x19|\x1A|\x1B|\x1C|\x1D|\x1E|\x1F|" +
        @"\x7F|\xFF|\xFE)",
        RegexOptions.Compiled | RegexOptions.IgnoreCase);

    private static readonly HashSet<byte> _suspiciousBytes = new()
    {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x0B, 0x0C, 0x0E, 0x0F,
        0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,
        0x7F, 0xFF, 0xFE
    };

    /// <summary>
    /// 验证消息数据
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="config">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateMessageData(ReadOnlySpan<byte> data, TcpClientConfig? config, ILogger? logger = null)
    {
        // 使用默认配置如果未提供
        var validationConfig = config ?? new TcpClientConfig();
        var errors = new List<string>();

        // 1. 检查数据长度
        if (data.Length == 0)
        {
            errors.Add("消息数据不能为空");
        }
        else if (data.Length > validationConfig.MaxMessageLength)
        {
            errors.Add($"消息长度 {data.Length} 超过最大限制 {validationConfig.MaxMessageLength}");
            logger?.LogWarning("消息长度超限: {Length}/{MaxLength}", data.Length, validationConfig.MaxMessageLength);
        }

        // 2. 检查恶意字节序列
        var suspiciousCount = CountSuspiciousBytes(data);
        if (suspiciousCount > 0)
        {
            var suspiciousRatio = (double)suspiciousCount / data.Length;
            if (suspiciousRatio > 0.1) // 超过10%的可疑字节
            {
                errors.Add($"检测到过多可疑字节: {suspiciousCount}/{data.Length} ({suspiciousRatio:P1})");
                logger?.LogWarning("检测到可疑数据模式: {SuspiciousCount}/{TotalLength}", suspiciousCount, data.Length);
            }
        }

        // 3. 检查数据熵（检测随机数据或加密数据）
        var entropy = CalculateEntropy(data);
        if (entropy > 7.5) // 高熵值可能表示加密数据或随机数据
        {
            logger?.LogDebug("检测到高熵数据: {Entropy:F2}", entropy);
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证文本消息
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <param name="config">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateTextMessage(string text, TcpClientConfig? config, ILogger? logger = null)
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(text))
        {
            errors.Add("文本消息不能为空");
            return new ValidationResult(false, errors);
        }

        // 使用默认配置如果未提供
        var validationConfig = config ?? new TcpClientConfig();

        // 1. 检查文本长度
        var textBytes = Encoding.UTF8.GetBytes(text);
        if (textBytes.Length > validationConfig.MaxMessageLength)
        {
            errors.Add($"文本消息编码后长度 {textBytes.Length} 超过最大限制 {validationConfig.MaxMessageLength}");
            logger?.LogWarning("文本消息长度超限: {Length}/{MaxLength}", textBytes.Length, validationConfig.MaxMessageLength);
        }

        // 2. 检查恶意模式
        if (_maliciousPatterns.IsMatch(text))
        {
            errors.Add("文本包含可疑的控制字符");
            logger?.LogWarning("检测到文本中的恶意模式");
        }

        // 3. 检查字符编码有效性
        try
        {
            var roundTrip = Encoding.UTF8.GetString(textBytes);
            if (!string.Equals(text, roundTrip, StringComparison.Ordinal))
            {
                errors.Add("文本包含无效的UTF-8字符序列");
                logger?.LogWarning("检测到无效的UTF-8编码");
            }
        }
        catch (Exception ex)
        {
            errors.Add($"文本编码验证失败: {ex.Message}");
            logger?.LogWarning(ex, "文本编码验证异常");
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证JSON消息
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <param name="config">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateJsonMessage(string json, TcpClientConfig? config, ILogger? logger = null)
    {
        var textValidation = ValidateTextMessage(json, config, logger);
        if (!textValidation.IsValid)
        {
            return textValidation;
        }

        // 使用默认配置如果未提供
        var validationConfig = config ?? new TcpClientConfig();
        var errors = new List<string>();

        try
        {
            // 验证JSON格式
            using var document = System.Text.Json.JsonDocument.Parse(json);

            // 检查JSON深度（防止深度嵌套攻击）
            var maxDepth = GetJsonDepth(document.RootElement);
            if (maxDepth > 20) // 限制最大深度
            {
                errors.Add($"JSON嵌套深度 {maxDepth} 超过安全限制 20");
                logger?.LogWarning("JSON深度超限: {Depth}", maxDepth);
            }

            // 检查JSON大小
            if (json.Length > validationConfig.MaxMessageLength / 2) // JSON通常会膨胀
            {
                logger?.LogDebug("JSON消息较大: {Size} bytes", json.Length);
            }
        }
        catch (System.Text.Json.JsonException ex)
        {
            errors.Add($"无效的JSON格式: {ex.Message}");
            logger?.LogWarning(ex, "JSON格式验证失败");
        }
        catch (Exception ex)
        {
            errors.Add($"JSON验证异常: {ex.Message}");
            logger?.LogError(ex, "JSON验证过程中发生异常");
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 验证消息头
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="dataLength">数据长度</param>
    /// <param name="config">客户端配置</param>
    /// <param name="logger">日志记录器</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateMessageHeader(byte messageType, int dataLength, TcpClientConfig? config, ILogger? logger = null)
    {
        // 使用默认配置如果未提供
        var validationConfig = config ?? new TcpClientConfig();
        var errors = new List<string>();

        // 1. 验证消息类型
        if (!IsValidMessageType(messageType))
        {
            errors.Add($"无效的消息类型: 0x{messageType:X2}");
            logger?.LogWarning("检测到无效消息类型: 0x{MessageType:X2}", messageType);
        }

        // 2. 验证数据长度
        if (dataLength < 0)
        {
            errors.Add($"无效的数据长度: {dataLength}");
            logger?.LogWarning("检测到负数数据长度: {DataLength}", dataLength);
        }
        else if (dataLength > validationConfig.MaxMessageLength)
        {
            errors.Add($"数据长度 {dataLength} 超过最大限制 {validationConfig.MaxMessageLength}");
            logger?.LogWarning("数据长度超限: {Length}/{MaxLength}", dataLength, validationConfig.MaxMessageLength);
        }

        return new ValidationResult(errors.Count == 0, errors);
    }

    /// <summary>
    /// 计算可疑字节数量
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>可疑字节数量</returns>
    private static int CountSuspiciousBytes(ReadOnlySpan<byte> data)
    {
        var count = 0;
        foreach (var b in data)
        {
            if (_suspiciousBytes.Contains(b))
            {
                count++;
            }
        }
        return count;
    }

    /// <summary>
    /// 计算数据熵
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>熵值</returns>
    private static double CalculateEntropy(ReadOnlySpan<byte> data)
    {
        if (data.Length == 0) return 0;

        var frequency = new int[256];
        foreach (var b in data)
        {
            frequency[b]++;
        }

        var entropy = 0.0;
        var length = data.Length;

        for (var i = 0; i < 256; i++)
        {
            if (frequency[i] > 0)
            {
                var probability = (double)frequency[i] / length;
                entropy -= probability * Math.Log2(probability);
            }
        }

        return entropy;
    }

    /// <summary>
    /// 获取JSON深度
    /// </summary>
    /// <param name="element">JSON元素</param>
    /// <returns>深度</returns>
    private static int GetJsonDepth(System.Text.Json.JsonElement element)
    {
        return element.ValueKind switch
        {
            System.Text.Json.JsonValueKind.Object => 1 + element.EnumerateObject().Max(p => GetJsonDepth(p.Value)),
            System.Text.Json.JsonValueKind.Array => 1 + element.EnumerateArray().Max(GetJsonDepth),
            _ => 0
        };
    }

    /// <summary>
    /// 验证消息类型是否有效
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>是否有效</returns>
    private static bool IsValidMessageType(byte messageType)
    {
        return messageType switch
        {
            TcpClientConstants.MessageTypes.Data => true,
            TcpClientConstants.MessageTypes.HeartbeatRequest => true,
            TcpClientConstants.MessageTypes.HeartbeatResponse => true,
            TcpClientConstants.MessageTypes.ConnectionAck => true,
            TcpClientConstants.MessageTypes.Disconnect => true,
            TcpClientConstants.MessageTypes.Error => true,
            _ => false
        };
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public IReadOnlyList<string> Errors { get; }

    /// <summary>
    /// 初始化验证结果
    /// </summary>
    /// <param name="isValid">是否有效</param>
    /// <param name="errors">错误列表</param>
    public ValidationResult(bool isValid, IEnumerable<string> errors)
    {
        IsValid = isValid;
        Errors = errors.ToList().AsReadOnly();
    }
}
