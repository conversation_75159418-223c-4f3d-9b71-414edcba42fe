namespace Liam.TcpClient.Models;

/// <summary>
/// 客户端统计信息
/// </summary>
public class ClientStatistics
{
    /// <summary>
    /// 总连接次数
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// 成功连接次数
    /// </summary>
    public long SuccessfulConnections { get; set; }

    /// <summary>
    /// 失败连接次数
    /// </summary>
    public long FailedConnections { get; set; }

    /// <summary>
    /// 总重连次数
    /// </summary>
    public long TotalReconnections { get; set; }

    /// <summary>
    /// 成功重连次数
    /// </summary>
    public long SuccessfulReconnections { get; set; }

    /// <summary>
    /// 失败重连次数
    /// </summary>
    public long FailedReconnections { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总发送消息数
    /// </summary>
    public long TotalMessagesSent { get; set; }

    /// <summary>
    /// 总接收消息数
    /// </summary>
    public long TotalMessagesReceived { get; set; }

    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }

    /// <summary>
    /// 心跳发送次数
    /// </summary>
    public long HeartbeatsSent { get; set; }

    /// <summary>
    /// 心跳接收次数
    /// </summary>
    public long HeartbeatsReceived { get; set; }

    /// <summary>
    /// 心跳超时次数
    /// </summary>
    public long HeartbeatTimeouts { get; set; }

    /// <summary>
    /// 客户端启动时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 客户端运行时间
    /// </summary>
    public TimeSpan? Uptime => StartedAt.HasValue ? DateTime.UtcNow - StartedAt.Value : null;

    /// <summary>
    /// 当前连接信息
    /// </summary>
    public ConnectionInfo? CurrentConnection { get; set; }

    /// <summary>
    /// 平均连接持续时间
    /// </summary>
    public TimeSpan AverageConnectionDuration { get; set; }

    /// <summary>
    /// 平均发送速度（字节/秒）
    /// </summary>
    public double AverageSendRate { get; set; }

    /// <summary>
    /// 平均接收速度（字节/秒）
    /// </summary>
    public double AverageReceiveRate { get; set; }

    /// <summary>
    /// 连接成功率
    /// </summary>
    public double ConnectionSuccessRate => TotalConnections > 0 ? (double)SuccessfulConnections / TotalConnections * 100 : 0;

    /// <summary>
    /// 重连成功率
    /// </summary>
    public double ReconnectionSuccessRate => TotalReconnections > 0 ? (double)SuccessfulReconnections / TotalReconnections * 100 : 0;

    /// <summary>
    /// 错误率
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 心跳响应率
    /// </summary>
    public double HeartbeatResponseRate => HeartbeatsSent > 0 ? (double)HeartbeatsReceived / HeartbeatsSent * 100 : 0;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 性能指标
    /// </summary>
    public PerformanceMetrics Performance { get; } = new();

    /// <summary>
    /// 更新统计信息
    /// </summary>
    public void Update()
    {
        LastUpdatedAt = DateTime.UtcNow;
        
        // 计算平均速度
        if (Uptime.HasValue && Uptime.Value.TotalSeconds > 0)
        {
            AverageSendRate = TotalBytesSent / Uptime.Value.TotalSeconds;
            AverageReceiveRate = TotalBytesReceived / Uptime.Value.TotalSeconds;
        }

        // 计算错误率
        var totalOperations = TotalConnections + TotalMessagesSent + TotalMessagesReceived;
        ErrorRate = totalOperations > 0 ? (double)TotalErrors / totalOperations * 100 : 0;
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        TotalConnections = 0;
        SuccessfulConnections = 0;
        FailedConnections = 0;
        TotalReconnections = 0;
        SuccessfulReconnections = 0;
        FailedReconnections = 0;
        TotalBytesSent = 0;
        TotalBytesReceived = 0;
        TotalMessagesSent = 0;
        TotalMessagesReceived = 0;
        TotalErrors = 0;
        HeartbeatsSent = 0;
        HeartbeatsReceived = 0;
        HeartbeatTimeouts = 0;
        StartedAt = DateTime.UtcNow;
        AverageConnectionDuration = TimeSpan.Zero;
        AverageSendRate = 0;
        AverageReceiveRate = 0;
        ErrorRate = 0;
        LastUpdatedAt = DateTime.UtcNow;
        Performance.Reset();
    }

    /// <summary>
    /// 记录连接尝试
    /// </summary>
    /// <param name="success">是否成功</param>
    public void RecordConnectionAttempt(bool success)
    {
        TotalConnections++;
        if (success)
        {
            SuccessfulConnections++;
        }
        else
        {
            FailedConnections++;
        }
    }

    /// <summary>
    /// 记录重连尝试
    /// </summary>
    /// <param name="success">是否成功</param>
    public void RecordReconnectionAttempt(bool success)
    {
        TotalReconnections++;
        if (success)
        {
            SuccessfulReconnections++;
        }
        else
        {
            FailedReconnections++;
        }
    }

    /// <summary>
    /// 记录数据发送
    /// </summary>
    /// <param name="bytes">字节数</param>
    public void RecordDataSent(int bytes)
    {
        TotalBytesSent += bytes;
        TotalMessagesSent++;
    }

    /// <summary>
    /// 记录数据接收
    /// </summary>
    /// <param name="bytes">字节数</param>
    public void RecordDataReceived(int bytes)
    {
        TotalBytesReceived += bytes;
        TotalMessagesReceived++;
    }

    /// <summary>
    /// 记录错误
    /// </summary>
    public void RecordError()
    {
        TotalErrors++;
    }

    /// <summary>
    /// 记录心跳发送
    /// </summary>
    public void RecordHeartbeatSent()
    {
        HeartbeatsSent++;
    }

    /// <summary>
    /// 记录心跳接收
    /// </summary>
    public void RecordHeartbeatReceived()
    {
        HeartbeatsReceived++;
    }

    /// <summary>
    /// 记录心跳超时
    /// </summary>
    public void RecordHeartbeatTimeout()
    {
        HeartbeatTimeouts++;
    }
}

/// <summary>
/// 连接统计信息
/// </summary>
public class ConnectionStatistics
{
    /// <summary>
    /// 发送字节数
    /// </summary>
    public long BytesSent { get; set; }

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long BytesReceived { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    public long MessagesSent { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    public long MessagesReceived { get; set; }

    /// <summary>
    /// 错误数
    /// </summary>
    public long Errors { get; set; }

    /// <summary>
    /// 最后发送时间
    /// </summary>
    public DateTime? LastSentAt { get; set; }

    /// <summary>
    /// 最后接收时间
    /// </summary>
    public DateTime? LastReceivedAt { get; set; }

    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double AverageLatency { get; set; }

    /// <summary>
    /// 最小延迟（毫秒）
    /// </summary>
    public double MinLatency { get; set; } = double.MaxValue;

    /// <summary>
    /// 最大延迟（毫秒）
    /// </summary>
    public double MaxLatency { get; set; }

    /// <summary>
    /// 延迟样本数
    /// </summary>
    public long LatencySamples { get; set; }

    /// <summary>
    /// 记录数据发送
    /// </summary>
    /// <param name="bytes">字节数</param>
    public void RecordSent(int bytes)
    {
        BytesSent += bytes;
        MessagesSent++;
        LastSentAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 记录数据接收
    /// </summary>
    /// <param name="bytes">字节数</param>
    public void RecordReceived(int bytes)
    {
        BytesReceived += bytes;
        MessagesReceived++;
        LastReceivedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 记录延迟
    /// </summary>
    /// <param name="latency">延迟（毫秒）</param>
    public void RecordLatency(double latency)
    {
        if (latency < MinLatency)
        {
            MinLatency = latency;
        }

        if (latency > MaxLatency)
        {
            MaxLatency = latency;
        }

        // 计算移动平均
        AverageLatency = (AverageLatency * LatencySamples + latency) / (LatencySamples + 1);
        LatencySamples++;
    }

    /// <summary>
    /// 记录错误
    /// </summary>
    public void RecordError()
    {
        Errors++;
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        BytesSent = 0;
        BytesReceived = 0;
        MessagesSent = 0;
        MessagesReceived = 0;
        Errors = 0;
        LastSentAt = null;
        LastReceivedAt = null;
        AverageLatency = 0;
        MinLatency = double.MaxValue;
        MaxLatency = 0;
        LatencySamples = 0;
    }
}

/// <summary>
/// 性能指标
/// </summary>
public class PerformanceMetrics
{
    /// <summary>
    /// CPU使用率
    /// </summary>
    public double CpuUsage { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// 网络延迟（毫秒）
    /// </summary>
    public double NetworkLatency { get; set; }

    /// <summary>
    /// 吞吐量（字节/秒）
    /// </summary>
    public double Throughput { get; set; }

    /// <summary>
    /// 连接质量评分（0-100）
    /// </summary>
    public double ConnectionQuality { get; set; }

    /// <summary>
    /// 重置性能指标
    /// </summary>
    public void Reset()
    {
        CpuUsage = 0;
        MemoryUsage = 0;
        NetworkLatency = 0;
        Throughput = 0;
        ConnectionQuality = 0;
    }
}
