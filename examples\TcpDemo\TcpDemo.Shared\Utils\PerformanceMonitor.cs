using System.Diagnostics;
using TcpDemo.Shared.Models;

namespace TcpDemo.Shared.Utils;

/// <summary>
/// 性能监控器
/// </summary>
public class PerformanceMonitor : IDisposable
{
    private readonly Stopwatch _stopwatch;
    private readonly Process _currentProcess;
    private readonly List<double> _latencies;
    private readonly object _lock = new();
    private bool _disposed;

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; private set; }

    /// <summary>
    /// 总操作数
    /// </summary>
    public long TotalOperations { get; private set; }

    /// <summary>
    /// 成功操作数
    /// </summary>
    public long SuccessfulOperations { get; private set; }

    /// <summary>
    /// 失败操作数
    /// </summary>
    public long FailedOperations { get; private set; }

    /// <summary>
    /// 总传输字节数
    /// </summary>
    public long TotalBytesTransferred { get; private set; }

    /// <summary>
    /// 初始内存使用量
    /// </summary>
    public long InitialMemoryUsage { get; private set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public PerformanceMonitor()
    {
        _stopwatch = new Stopwatch();
        _currentProcess = Process.GetCurrentProcess();
        _latencies = new List<double>();
        InitialMemoryUsage = GC.GetTotalMemory(false);
    }

    /// <summary>
    /// 开始监控
    /// </summary>
    public void Start()
    {
        StartTime = DateTime.UtcNow;
        _stopwatch.Start();
    }

    /// <summary>
    /// 停止监控
    /// </summary>
    public void Stop()
    {
        _stopwatch.Stop();
    }

    /// <summary>
    /// 记录操作
    /// </summary>
    /// <param name="success">是否成功</param>
    /// <param name="latencyMs">延迟（毫秒）</param>
    /// <param name="bytesTransferred">传输字节数</param>
    public void RecordOperation(bool success, double latencyMs = 0, long bytesTransferred = 0)
    {
        lock (_lock)
        {
            TotalOperations++;
            if (success)
                SuccessfulOperations++;
            else
                FailedOperations++;

            TotalBytesTransferred += bytesTransferred;

            if (latencyMs > 0)
                _latencies.Add(latencyMs);
        }
    }

    /// <summary>
    /// 获取当前内存使用量（字节）
    /// </summary>
    /// <returns>内存使用量</returns>
    public long GetCurrentMemoryUsage()
    {
        return GC.GetTotalMemory(false);
    }

    /// <summary>
    /// 获取内存增长量（字节）
    /// </summary>
    /// <returns>内存增长量</returns>
    public long GetMemoryGrowth()
    {
        return GetCurrentMemoryUsage() - InitialMemoryUsage;
    }

    /// <summary>
    /// 获取CPU使用率（百分比）
    /// </summary>
    /// <returns>CPU使用率</returns>
    public double GetCpuUsage()
    {
        try
        {
            return _currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.ProcessorCount / _stopwatch.ElapsedMilliseconds * 100;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 获取平均延迟（毫秒）
    /// </summary>
    /// <returns>平均延迟</returns>
    public double GetAverageLatency()
    {
        lock (_lock)
        {
            return _latencies.Count > 0 ? _latencies.Average() : 0;
        }
    }

    /// <summary>
    /// 获取最小延迟（毫秒）
    /// </summary>
    /// <returns>最小延迟</returns>
    public double GetMinLatency()
    {
        lock (_lock)
        {
            return _latencies.Count > 0 ? _latencies.Min() : 0;
        }
    }

    /// <summary>
    /// 获取最大延迟（毫秒）
    /// </summary>
    /// <returns>最大延迟</returns>
    public double GetMaxLatency()
    {
        lock (_lock)
        {
            return _latencies.Count > 0 ? _latencies.Max() : 0;
        }
    }

    /// <summary>
    /// 获取吞吐量（操作/秒）
    /// </summary>
    /// <returns>吞吐量</returns>
    public double GetThroughput()
    {
        var elapsedSeconds = _stopwatch.Elapsed.TotalSeconds;
        return elapsedSeconds > 0 ? TotalOperations / elapsedSeconds : 0;
    }

    /// <summary>
    /// 获取传输速度（字节/秒）
    /// </summary>
    /// <returns>传输速度</returns>
    public double GetTransferRate()
    {
        var elapsedSeconds = _stopwatch.Elapsed.TotalSeconds;
        return elapsedSeconds > 0 ? TotalBytesTransferred / elapsedSeconds : 0;
    }

    /// <summary>
    /// 获取成功率
    /// </summary>
    /// <returns>成功率</returns>
    public double GetSuccessRate()
    {
        return TotalOperations > 0 ? (double)SuccessfulOperations / TotalOperations : 0;
    }

    /// <summary>
    /// 获取错误率
    /// </summary>
    /// <returns>错误率</returns>
    public double GetErrorRate()
    {
        return TotalOperations > 0 ? (double)FailedOperations / TotalOperations : 0;
    }

    /// <summary>
    /// 生成测试结果
    /// </summary>
    /// <param name="scenarioId">场景ID</param>
    /// <param name="scenarioName">场景名称</param>
    /// <param name="status">测试状态</param>
    /// <returns>测试结果</returns>
    public TestResult GenerateResult(string scenarioId, string scenarioName, TestResultStatus status = TestResultStatus.Success)
    {
        var result = new TestResult
        {
            ScenarioId = scenarioId,
            ScenarioName = scenarioName,
            Status = status,
            StartTime = StartTime,
            EndTime = DateTime.UtcNow,
            TotalOperations = TotalOperations,
            SuccessfulOperations = SuccessfulOperations,
            FailedOperations = FailedOperations,
            SuccessRate = GetSuccessRate(),
            ErrorRate = GetErrorRate(),
            AverageLatencyMs = GetAverageLatency(),
            MinLatencyMs = GetMinLatency(),
            MaxLatencyMs = GetMaxLatency(),
            ThroughputOpsPerSec = GetThroughput(),
            TotalBytesTransferred = TotalBytesTransferred,
            TransferRateBytesPerSec = GetTransferRate(),
            MemoryUsageBytes = GetCurrentMemoryUsage(),
            CpuUsagePercent = GetCpuUsage()
        };

        // 添加详细指标
        result.AddMetric("ElapsedTimeMs", _stopwatch.ElapsedMilliseconds);
        result.AddMetric("MemoryGrowthBytes", GetMemoryGrowth());
        result.AddMetric("LatencyCount", _latencies.Count);
        result.AddMetric("ProcessorCount", Environment.ProcessorCount);

        if (_latencies.Count > 0)
        {
            result.AddMetric("LatencyP50", GetPercentile(0.5));
            result.AddMetric("LatencyP90", GetPercentile(0.9));
            result.AddMetric("LatencyP95", GetPercentile(0.95));
            result.AddMetric("LatencyP99", GetPercentile(0.99));
        }

        return result;
    }

    /// <summary>
    /// 获取延迟百分位数
    /// </summary>
    /// <param name="percentile">百分位数（0-1）</param>
    /// <returns>延迟值</returns>
    private double GetPercentile(double percentile)
    {
        lock (_lock)
        {
            if (_latencies.Count == 0) return 0;

            var sorted = _latencies.OrderBy(x => x).ToList();
            var index = (int)Math.Ceiling(percentile * sorted.Count) - 1;
            return sorted[Math.Max(0, Math.Min(index, sorted.Count - 1))];
        }
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void Reset()
    {
        lock (_lock)
        {
            _stopwatch.Reset();
            TotalOperations = 0;
            SuccessfulOperations = 0;
            FailedOperations = 0;
            TotalBytesTransferred = 0;
            _latencies.Clear();
            InitialMemoryUsage = GC.GetTotalMemory(false);
        }
    }

    /// <summary>
    /// 获取实时统计信息
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public string GetRealTimeStats()
    {
        var elapsed = _stopwatch.Elapsed;
        var throughput = GetThroughput();
        var transferRate = GetTransferRate() / 1024.0 / 1024.0; // MB/s
        var memoryMB = GetCurrentMemoryUsage() / 1024.0 / 1024.0;

        return $"运行时间: {elapsed:hh\\:mm\\:ss} | " +
               $"操作数: {TotalOperations} | " +
               $"成功率: {GetSuccessRate():P1} | " +
               $"吞吐量: {throughput:F1} ops/s | " +
               $"传输: {transferRate:F2} MB/s | " +
               $"内存: {memoryMB:F1} MB";
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _stopwatch?.Stop();
            _currentProcess?.Dispose();
            _disposed = true;
        }
    }
}
