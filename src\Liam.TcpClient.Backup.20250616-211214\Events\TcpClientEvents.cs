using Liam.TcpClient.Models;

namespace Liam.TcpClient.Events;

/// <summary>
/// 连接状态变更事件参数
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 旧状态
    /// </summary>
    public string OldState { get; }

    /// <summary>
    /// 新状态
    /// </summary>
    public string NewState { get; }

    /// <summary>
    /// 状态变更时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 初始化连接状态变更事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="oldState">旧状态</param>
    /// <param name="newState">新状态</param>
    public ConnectionStateChangedEventArgs(ConnectionInfo connectionInfo, string oldState, string newState)
    {
        ConnectionInfo = connectionInfo;
        OldState = oldState;
        NewState = newState;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// 连接建立事件参数
/// </summary>
public class ConnectedEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime ConnectedAt { get; }

    /// <summary>
    /// 是否为重连
    /// </summary>
    public bool IsReconnection { get; }

    /// <summary>
    /// 初始化连接建立事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="isReconnection">是否为重连</param>
    public ConnectedEventArgs(ConnectionInfo connectionInfo, bool isReconnection = false)
    {
        ConnectionInfo = connectionInfo;
        ConnectedAt = DateTime.UtcNow;
        IsReconnection = isReconnection;
    }
}

/// <summary>
/// 连接断开事件参数
/// </summary>
public class DisconnectedEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 断开时间
    /// </summary>
    public DateTime DisconnectedAt { get; }

    /// <summary>
    /// 断开原因
    /// </summary>
    public string? Reason { get; }

    /// <summary>
    /// 是否为意外断开
    /// </summary>
    public bool IsUnexpected { get; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; }

    /// <summary>
    /// 初始化连接断开事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="reason">断开原因</param>
    /// <param name="isUnexpected">是否为意外断开</param>
    /// <param name="exception">异常信息</param>
    public DisconnectedEventArgs(ConnectionInfo connectionInfo, string? reason = null, 
        bool isUnexpected = false, Exception? exception = null)
    {
        ConnectionInfo = connectionInfo;
        DisconnectedAt = DateTime.UtcNow;
        Reason = reason;
        IsUnexpected = isUnexpected;
        Exception = exception;
    }
}

/// <summary>
/// 数据接收事件参数
/// </summary>
public class DataReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 接收的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => Data?.Length ?? 0;

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; }

    /// <summary>
    /// 初始化数据接收事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="data">接收的数据</param>
    public DataReceivedEventArgs(ConnectionInfo connectionInfo, byte[] data)
    {
        ConnectionInfo = connectionInfo;
        Data = data ?? Array.Empty<byte>();
        ReceivedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 数据发送事件参数
/// </summary>
public class DataSentEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 发送的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => Data?.Length ?? 0;

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SentAt { get; }

    /// <summary>
    /// 是否发送成功
    /// </summary>
    public bool Success { get; }

    /// <summary>
    /// 初始化数据发送事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="data">发送的数据</param>
    /// <param name="success">是否发送成功</param>
    public DataSentEventArgs(ConnectionInfo connectionInfo, byte[] data, bool success = true)
    {
        ConnectionInfo = connectionInfo;
        Data = data ?? Array.Empty<byte>();
        SentAt = DateTime.UtcNow;
        Success = success;
    }
}

/// <summary>
/// 消息接收事件参数
/// </summary>
public class MessageReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 接收的消息
    /// </summary>
    public TcpMessage Message { get; }

    /// <summary>
    /// 接收时间
    /// </summary>
    public DateTime ReceivedAt { get; }

    /// <summary>
    /// 初始化消息接收事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="message">接收的消息</param>
    public MessageReceivedEventArgs(ConnectionInfo connectionInfo, TcpMessage message)
    {
        ConnectionInfo = connectionInfo;
        Message = message;
        ReceivedAt = DateTime.UtcNow;
    }
}

/// <summary>
/// 消息发送事件参数
/// </summary>
public class MessageSentEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 发送的消息
    /// </summary>
    public TcpMessage Message { get; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime SentAt { get; }

    /// <summary>
    /// 是否发送成功
    /// </summary>
    public bool Success { get; }

    /// <summary>
    /// 初始化消息发送事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="message">发送的消息</param>
    /// <param name="success">是否发送成功</param>
    public MessageSentEventArgs(ConnectionInfo connectionInfo, TcpMessage message, bool success = true)
    {
        ConnectionInfo = connectionInfo;
        Message = message;
        SentAt = DateTime.UtcNow;
        Success = success;
    }
}

/// <summary>
/// 心跳事件参数
/// </summary>
public class HeartbeatEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo ConnectionInfo { get; }

    /// <summary>
    /// 心跳类型
    /// </summary>
    public HeartbeatType Type { get; }

    /// <summary>
    /// 响应时间（毫秒）
    /// </summary>
    public double? ResponseTime { get; }

    /// <summary>
    /// 心跳时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 初始化心跳事件参数
    /// </summary>
    /// <param name="connectionInfo">连接信息</param>
    /// <param name="type">心跳类型</param>
    /// <param name="responseTime">响应时间</param>
    public HeartbeatEventArgs(ConnectionInfo connectionInfo, HeartbeatType type, double? responseTime = null)
    {
        ConnectionInfo = connectionInfo;
        Type = type;
        ResponseTime = responseTime;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// 心跳类型
/// </summary>
public enum HeartbeatType
{
    /// <summary>
    /// 发送心跳请求
    /// </summary>
    Request,

    /// <summary>
    /// 接收心跳响应
    /// </summary>
    Response,

    /// <summary>
    /// 心跳超时
    /// </summary>
    Timeout
}

/// <summary>
/// 错误事件参数
/// </summary>
public class TcpClientErrorEventArgs : EventArgs
{
    /// <summary>
    /// 连接信息
    /// </summary>
    public ConnectionInfo? ConnectionInfo { get; }

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; }

    /// <summary>
    /// 错误时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 错误类型
    /// </summary>
    public string ErrorType { get; }

    /// <summary>
    /// 初始化错误事件参数
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="connectionInfo">连接信息</param>
    public TcpClientErrorEventArgs(Exception exception, ConnectionInfo? connectionInfo = null)
    {
        Exception = exception;
        ConnectionInfo = connectionInfo;
        Message = exception.Message;
        Timestamp = DateTime.UtcNow;
        ErrorType = exception.GetType().Name;
    }

    /// <summary>
    /// 初始化错误事件参数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="connectionInfo">连接信息</param>
    public TcpClientErrorEventArgs(string message, ConnectionInfo? connectionInfo = null)
    {
        Exception = new Exception(message);
        ConnectionInfo = connectionInfo;
        Message = message;
        Timestamp = DateTime.UtcNow;
        ErrorType = "General";
    }
}
