using System.Text.Json;

namespace TcpDemo.Shared.Models;

/// <summary>
/// 演示消息类型
/// </summary>
public enum DemoMessageType
{
    /// <summary>
    /// 文本消息
    /// </summary>
    Text = 1,

    /// <summary>
    /// 命令消息
    /// </summary>
    Command = 2,

    /// <summary>
    /// 心跳消息
    /// </summary>
    Heartbeat = 3,

    /// <summary>
    /// 文件传输
    /// </summary>
    FileTransfer = 4,

    /// <summary>
    /// 性能测试
    /// </summary>
    PerformanceTest = 5,

    /// <summary>
    /// 错误消息
    /// </summary>
    Error = 6,

    /// <summary>
    /// 响应消息
    /// </summary>
    Response = 7
}

/// <summary>
/// 演示消息
/// </summary>
public class DemoMessage
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 消息类型
    /// </summary>
    public DemoMessageType Type { get; set; }

    /// <summary>
    /// 发送者ID
    /// </summary>
    public string? SenderId { get; set; }

    /// <summary>
    /// 接收者ID
    /// </summary>
    public string? ReceiverId { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 消息数据
    /// </summary>
    public byte[]? Data { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 扩展属性
    /// </summary>
    public Dictionary<string, object>? Properties { get; set; }

    /// <summary>
    /// 序列化为JSON
    /// </summary>
    /// <returns>JSON字符串</returns>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        });
    }

    /// <summary>
    /// 从JSON反序列化
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <returns>消息对象</returns>
    public static DemoMessage? FromJson(string json)
    {
        try
        {
            return JsonSerializer.Deserialize<DemoMessage>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 创建文本消息
    /// </summary>
    /// <param name="content">文本内容</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>文本消息</returns>
    public static DemoMessage CreateTextMessage(string content, string? senderId = null, string? receiverId = null)
    {
        return new DemoMessage
        {
            Type = DemoMessageType.Text,
            Content = content,
            SenderId = senderId,
            ReceiverId = receiverId
        };
    }

    /// <summary>
    /// 创建命令消息
    /// </summary>
    /// <param name="command">命令</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>命令消息</returns>
    public static DemoMessage CreateCommandMessage(string command, string? senderId = null, string? receiverId = null)
    {
        return new DemoMessage
        {
            Type = DemoMessageType.Command,
            Content = command,
            SenderId = senderId,
            ReceiverId = receiverId
        };
    }

    /// <summary>
    /// 创建心跳消息
    /// </summary>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>心跳消息</returns>
    public static DemoMessage CreateHeartbeatMessage(string? senderId = null, string? receiverId = null)
    {
        return new DemoMessage
        {
            Type = DemoMessageType.Heartbeat,
            Content = "ping",
            SenderId = senderId,
            ReceiverId = receiverId
        };
    }

    /// <summary>
    /// 创建响应消息
    /// </summary>
    /// <param name="originalMessage">原始消息</param>
    /// <param name="responseContent">响应内容</param>
    /// <param name="senderId">发送者ID</param>
    /// <returns>响应消息</returns>
    public static DemoMessage CreateResponseMessage(DemoMessage originalMessage, string responseContent, string? senderId = null)
    {
        return new DemoMessage
        {
            Type = DemoMessageType.Response,
            Content = responseContent,
            SenderId = senderId,
            ReceiverId = originalMessage.SenderId,
            Properties = new Dictionary<string, object>
            {
                ["OriginalMessageId"] = originalMessage.Id,
                ["OriginalMessageType"] = originalMessage.Type.ToString()
            }
        };
    }

    /// <summary>
    /// 创建错误消息
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="senderId">发送者ID</param>
    /// <param name="receiverId">接收者ID</param>
    /// <returns>错误消息</returns>
    public static DemoMessage CreateErrorMessage(string errorMessage, string? senderId = null, string? receiverId = null)
    {
        return new DemoMessage
        {
            Type = DemoMessageType.Error,
            Content = errorMessage,
            SenderId = senderId,
            ReceiverId = receiverId
        };
    }

    /// <summary>
    /// 获取消息摘要
    /// </summary>
    /// <returns>消息摘要</returns>
    public string GetSummary()
    {
        var content = Content?.Length > 50 ? Content[..50] + "..." : Content ?? "";
        return $"[{Type}] {Id[..8]} | {SenderId} -> {ReceiverId} | {content}";
    }

    /// <summary>
    /// 获取消息大小（字节）
    /// </summary>
    /// <returns>消息大小</returns>
    public int GetSize()
    {
        var json = ToJson();
        return System.Text.Encoding.UTF8.GetByteCount(json) + (Data?.Length ?? 0);
    }
}
