using System.Buffers;
using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;

namespace Liam.TcpClient.Performance;

/// <summary>
/// 高性能字符串操作优化器
/// </summary>
public static class StringOptimizer
{
    private static readonly ConcurrentQueue<StringBuilder> _stringBuilderPool = new();
    private static readonly ConcurrentDictionary<string, string> _stringCache = new();
    private static readonly int _maxCacheSize = 1000;
    private static readonly int _stringBuilderCapacity = 1024;
    private static readonly int _maxPoolSize = 100;

    /// <summary>
    /// 获取池化的StringBuilder
    /// </summary>
    /// <returns>StringBuilder实例</returns>
    public static StringBuilder GetStringBuilder()
    {
        if (_stringBuilderPool.TryDequeue(out var sb))
        {
            sb.Clear();
            return sb;
        }
        return new StringBuilder(_stringBuilderCapacity);
    }

    /// <summary>
    /// 归还StringBuilder到池中
    /// </summary>
    /// <param name="sb">StringBuilder实例</param>
    public static void ReturnStringBuilder(StringBuilder sb)
    {
        if (sb.Capacity <= _stringBuilderCapacity * 4 && _stringBuilderPool.Count < _maxPoolSize)
        {
            _stringBuilderPool.Enqueue(sb);
        }
    }

    /// <summary>
    /// 使用StringBuilder构建字符串
    /// </summary>
    /// <param name="builderAction">构建操作</param>
    /// <returns>构建的字符串</returns>
    public static string BuildString(Action<StringBuilder> builderAction)
    {
        var sb = GetStringBuilder();
        try
        {
            builderAction(sb);
            return sb.ToString();
        }
        finally
        {
            ReturnStringBuilder(sb);
        }
    }

    /// <summary>
    /// 缓存常用字符串
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <param name="valueFactory">值工厂</param>
    /// <returns>缓存的字符串</returns>
    public static string GetCachedString(string key, Func<string> valueFactory)
    {
        if (_stringCache.TryGetValue(key, out var cached))
        {
            return cached;
        }

        // 防止缓存过大
        if (_stringCache.Count >= _maxCacheSize)
        {
            // 清理一半的缓存
            var keysToRemove = _stringCache.Keys.Take(_maxCacheSize / 2).ToList();
            foreach (var keyToRemove in keysToRemove)
            {
                _stringCache.TryRemove(keyToRemove, out _);
            }
        }

        var value = valueFactory();
        _stringCache.TryAdd(key, value);
        return value;
    }

    /// <summary>
    /// 高性能JSON序列化到流
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要序列化的对象</param>
    /// <param name="stream">目标流</param>
    /// <param name="options">JSON选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化任务</returns>
    public static async Task SerializeToStreamAsync<T>(T obj, Stream stream, JsonSerializerOptions? options = null, CancellationToken cancellationToken = default)
    {
        await JsonSerializer.SerializeAsync(stream, obj, options, cancellationToken).ConfigureAwait(false);
    }

    /// <summary>
    /// 高性能JSON序列化到字节数组
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要序列化的对象</param>
    /// <param name="options">JSON选项</param>
    /// <returns>序列化的字节数组</returns>
    public static byte[] SerializeToBytes<T>(T obj, JsonSerializerOptions? options = null)
    {
        return JsonSerializer.SerializeToUtf8Bytes(obj, options);
    }

    /// <summary>
    /// 高性能字符串格式化
    /// </summary>
    /// <param name="format">格式字符串</param>
    /// <param name="args">参数</param>
    /// <returns>格式化的字符串</returns>
    public static string Format(string format, params object[] args)
    {
        if (args.Length == 0)
        {
            return format;
        }

        return BuildString(sb => sb.AppendFormat(format, args));
    }

    /// <summary>
    /// 高性能字符串连接
    /// </summary>
    /// <param name="separator">分隔符</param>
    /// <param name="values">值集合</param>
    /// <returns>连接的字符串</returns>
    public static string Join(string separator, IEnumerable<string> values)
    {
        return BuildString(sb =>
        {
            var first = true;
            foreach (var value in values)
            {
                if (!first)
                {
                    sb.Append(separator);
                }
                sb.Append(value);
                first = false;
            }
        });
    }

    /// <summary>
    /// 高性能字符串连接（对象版本）
    /// </summary>
    /// <param name="separator">分隔符</param>
    /// <param name="values">值集合</param>
    /// <returns>连接的字符串</returns>
    public static string Join<T>(string separator, IEnumerable<T> values)
    {
        return BuildString(sb =>
        {
            var first = true;
            foreach (var value in values)
            {
                if (!first)
                {
                    sb.Append(separator);
                }
                sb.Append(value?.ToString());
                first = false;
            }
        });
    }

    /// <summary>
    /// 构建异常详细信息
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="includeStackTrace">是否包含堆栈跟踪</param>
    /// <returns>异常详细信息</returns>
    public static string BuildExceptionDetails(Exception exception, bool includeStackTrace = true)
    {
        return BuildString(sb =>
        {
            sb.Append("异常类型: ").AppendLine(exception.GetType().Name);
            sb.Append("消息: ").AppendLine(exception.Message);
            
            if (includeStackTrace && !string.IsNullOrEmpty(exception.StackTrace))
            {
                sb.AppendLine("堆栈跟踪:");
                sb.AppendLine(exception.StackTrace);
            }

            var innerEx = exception.InnerException;
            var level = 1;
            while (innerEx != null && level <= 5) // 限制内部异常层级
            {
                sb.Append("内部异常 ").Append(level).Append(": ");
                sb.Append(innerEx.GetType().Name).Append(" - ");
                sb.AppendLine(innerEx.Message);
                innerEx = innerEx.InnerException;
                level++;
            }
        });
    }

    /// <summary>
    /// 构建连接信息字符串
    /// </summary>
    /// <param name="host">主机</param>
    /// <param name="port">端口</param>
    /// <param name="isSecure">是否安全连接</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>连接信息字符串</returns>
    public static string BuildConnectionInfo(string host, int port, bool isSecure, string? clientId = null)
    {
        var cacheKey = $"conn_{host}_{port}_{isSecure}_{clientId ?? "null"}";
        return GetCachedString(cacheKey, () =>
        {
            return BuildString(sb =>
            {
                sb.Append(isSecure ? "ssl://" : "tcp://");
                sb.Append(host).Append(':').Append(port);
                if (!string.IsNullOrEmpty(clientId))
                {
                    sb.Append(" (").Append(clientId).Append(')');
                }
            });
        });
    }

    /// <summary>
    /// 清理字符串缓存
    /// </summary>
    public static void ClearStringCache()
    {
        _stringCache.Clear();
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计</returns>
    public static (int Count, int MaxSize) GetCacheStats()
    {
        return (_stringCache.Count, _maxCacheSize);
    }
}


