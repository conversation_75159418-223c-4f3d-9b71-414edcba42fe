using Liam.TcpClient.Models;

namespace Liam.TcpClient.Exceptions;

/// <summary>
/// 配置异常
/// </summary>
public class ConfigurationException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Configuration;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => false;

    /// <summary>
    /// 初始化配置异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ConfigurationException(
        string message, 
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(message, innerException, connectionState, configuration, operationContext)
    {
        AddContext("Operation", "Configuration");
    }
}

/// <summary>
/// 验证异常
/// </summary>
public class ValidationException : TcpClientExceptionBase
{
    /// <summary>
    /// 验证错误列表
    /// </summary>
    public IReadOnlyList<string> ValidationErrors { get; }

    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Validation;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Warning;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => false;

    /// <summary>
    /// 初始化验证异常
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ValidationException(
        IEnumerable<string> validationErrors,
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(CreateMessage(validationErrors), innerException, connectionState, configuration, operationContext)
    {
        ValidationErrors = validationErrors.ToList().AsReadOnly();
        AddContext("Operation", "Validation");
        AddContext("ErrorCount", ValidationErrors.Count);
    }

    /// <summary>
    /// 创建异常消息
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    /// <returns>异常消息</returns>
    private static string CreateMessage(IEnumerable<string> validationErrors)
    {
        var errors = validationErrors.ToList();
        if (errors.Count == 0)
        {
            return "验证失败";
        }
        if (errors.Count == 1)
        {
            return $"验证失败: {errors[0]}";
        }
        return $"验证失败 ({errors.Count}个错误): {string.Join("; ", errors)}";
    }
}

/// <summary>
/// 安全异常
/// </summary>
public class SecurityException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Security;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Critical;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => false;

    /// <summary>
    /// 初始化安全异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public SecurityException(
        string message, 
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(message, innerException, connectionState, configuration, operationContext)
    {
        AddContext("Operation", "Security");
    }
}

/// <summary>
/// 协议异常
/// </summary>
public class ProtocolException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Protocol;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => true;

    /// <summary>
    /// 建议的重试延迟时间
    /// </summary>
    public override TimeSpan? SuggestedRetryDelay => TimeSpan.FromSeconds(2);

    /// <summary>
    /// 初始化协议异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ProtocolException(
        string message, 
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(message, innerException, connectionState, configuration, operationContext)
    {
        AddContext("Operation", "Protocol");
    }
}

/// <summary>
/// 资源异常
/// </summary>
public class ResourceException : TcpClientExceptionBase
{
    /// <summary>
    /// 异常分类
    /// </summary>
    public override TcpClientExceptionCategory Category => TcpClientExceptionCategory.Resource;

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public override TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public override bool IsRetryable => true;

    /// <summary>
    /// 建议的重试延迟时间
    /// </summary>
    public override TimeSpan? SuggestedRetryDelay => TimeSpan.FromSeconds(10);

    /// <summary>
    /// 初始化资源异常
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    public ResourceException(
        string message, 
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(message, innerException, connectionState, configuration, operationContext)
    {
        AddContext("Operation", "Resource");
    }
}
