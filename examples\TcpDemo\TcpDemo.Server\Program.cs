using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text;
using Liam.TcpServer.Extensions;
using Liam.TcpServer.Interfaces;
using Liam.Logging.Extensions;
using TcpDemo.Shared.Models;
using TcpDemo.Shared.Utils;

namespace TcpDemo.Server;

class Program
{
    private static ITcpServer? _tcpServer;
    private static ILogger<Program>? _logger;
    private static PerformanceMonitor? _performanceMonitor;
    private static readonly Dictionary<string, TestScenario> _activeScenarios = new();
    private static readonly Dictionary<string, TestResult> _testResults = new();

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpServer 完整功能测试应用 ===\n");

        // 解析命令行参数
        var config = ParseArguments(args);

        // 创建主机构建器
        var builder = Host.CreateDefaultBuilder(args);

        // 配置服务
        builder.ConfigureServices((context, services) =>
        {
            // 添加Liam.Logging服务
            services.AddLiamLogging(options =>
            {
                options.MinimumLevel = LogLevel.Information;
                options.EnableConsoleLogging = true;
                options.EnableFileLogging = true;
                options.LogDirectory = "logs";
                options.EnableStructuredLogging = true;
            });

            // 添加TCP服务器服务
            services.AddTcpServer(serverConfig =>
            {
                serverConfig.Port = config.Port;
                serverConfig.MaxConnections = config.MaxConnections;
                serverConfig.EnableHeartbeat = config.EnableHeartbeat;
                serverConfig.HeartbeatIntervalSeconds = config.HeartbeatInterval;
                serverConfig.HeartbeatTimeoutSeconds = config.HeartbeatTimeout;
                serverConfig.EnableSsl = config.EnableSsl;
                serverConfig.BufferSize = config.BufferSize;
            });
        });

        // 构建主机
        var host = builder.Build();

        // 获取服务实例
        _tcpServer = host.Services.GetRequiredService<ITcpServer>();
        _logger = host.Services.GetRequiredService<ILogger<Program>>();
        _performanceMonitor = new PerformanceMonitor();

        // 订阅事件
        SubscribeToServerEvents();

        try
        {
            // 启动服务器
            _logger.LogInformation("正在启动TCP服务器...");
            await _tcpServer.StartAsync();

            _logger.LogInformation("TCP服务器已启动，监听端口: {Port}", _tcpServer.Configuration.Port);
            _performanceMonitor.Start();

            DisplayServerInfo();
            DisplayCommands();

            // 处理控制台输入
            await HandleConsoleInput();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动服务器时发生错误");
            Console.WriteLine($"启动服务器时发生错误: {ex.Message}");
        }
        finally
        {
            // 停止服务器
            _logger.LogInformation("正在停止TCP服务器...");
            await _tcpServer.StopAsync();
            _performanceMonitor?.Stop();
            _performanceMonitor?.Dispose();
            _logger.LogInformation("TCP服务器已停止");
        }
    }

    /// <summary>
    /// 解析命令行参数
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>服务器配置</returns>
    private static ServerConfig ParseArguments(string[] args)
    {
        var config = new ServerConfig();

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--port" or "-p":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int port))
                    {
                        config.Port = port;
                        i++;
                    }
                    break;
                case "--max-connections" or "-mc":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int maxConn))
                    {
                        config.MaxConnections = maxConn;
                        i++;
                    }
                    break;
                case "--enable-heartbeat" or "-hb":
                    config.EnableHeartbeat = true;
                    break;
                case "--enable-ssl" or "-ssl":
                    config.EnableSsl = true;
                    break;
                case "--buffer-size" or "-bs":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int bufferSize))
                    {
                        config.BufferSize = bufferSize;
                        i++;
                    }
                    break;
                case "--help" or "-h":
                    DisplayHelp();
                    Environment.Exit(0);
                    break;
            }
        }

        return config;
    }

    /// <summary>
    /// 显示帮助信息
    /// </summary>
    private static void DisplayHelp()
    {
        Console.WriteLine("Liam.TcpServer 测试应用程序");
        Console.WriteLine();
        Console.WriteLine("用法: TcpDemo.Server [选项]");
        Console.WriteLine();
        Console.WriteLine("选项:");
        Console.WriteLine("  -p, --port <端口>              服务器监听端口 (默认: 8888)");
        Console.WriteLine("  -mc, --max-connections <数量>  最大连接数 (默认: 100)");
        Console.WriteLine("  -hb, --enable-heartbeat        启用心跳检测");
        Console.WriteLine("  -ssl, --enable-ssl             启用SSL/TLS");
        Console.WriteLine("  -bs, --buffer-size <大小>      缓冲区大小 (默认: 4096)");
        Console.WriteLine("  -h, --help                     显示帮助信息");
    }

    /// <summary>
    /// 订阅服务器事件
    /// </summary>
    private static void SubscribeToServerEvents()
    {
        if (_tcpServer == null) return;

        _tcpServer.ClientConnected += OnClientConnected;
        _tcpServer.ClientDisconnected += OnClientDisconnected;
        _tcpServer.DataReceived += OnDataReceived;
        _tcpServer.DataSent += OnDataSent;
        _tcpServer.Error += OnError;
        _tcpServer.Heartbeat += OnHeartbeat;
        _tcpServer.StatusChanged += OnStatusChanged;
    }

    /// <summary>
    /// 客户端连接事件处理
    /// </summary>
    private static void OnClientConnected(object? sender, Liam.TcpServer.Events.ClientConnectedEventArgs e)
    {
        var message = $"客户端已连接: {e.Connection.ClientIpAddress}:{e.Connection.ClientPort} (ID: {e.Connection.Id})";
        _logger?.LogInformation(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");

        _performanceMonitor?.RecordOperation(true);

        // 发送欢迎消息
        _ = Task.Run(async () =>
        {
            await Task.Delay(500);
            var welcomeMsg = DemoMessage.CreateTextMessage(
                $"欢迎连接到Liam.TcpServer测试服务器！连接ID: {e.Connection.Id}",
                DataGenerator.GenerateServerId(),
                e.Connection.Id
            );
            await _tcpServer!.SendTextAsync(e.Connection.Id, welcomeMsg.ToJson());
        });
    }

    /// <summary>
    /// 客户端断开事件处理
    /// </summary>
    private static void OnClientDisconnected(object? sender, Liam.TcpServer.Events.ClientDisconnectedEventArgs e)
    {
        var message = $"客户端已断开: {e.Connection.ClientIpAddress}:{e.Connection.ClientPort} (ID: {e.Connection.Id})";
        _logger?.LogInformation(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
        Console.WriteLine($"    断开原因: {e.Reason ?? "未指定"}");
        Console.WriteLine($"    连接持续时间: {e.Connection.ConnectionDuration:hh\\:mm\\:ss}");
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    private static async void OnDataReceived(object? sender, Liam.TcpServer.Events.DataReceivedEventArgs e)
    {
        try
        {
            var jsonData = Encoding.UTF8.GetString(e.Data);
            var message = DemoMessage.FromJson(jsonData);

            if (message != null)
            {
                _logger?.LogInformation("收到消息: {MessageSummary}", message.GetSummary());
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到消息: {message.GetSummary()}");

                _performanceMonitor?.RecordOperation(true, 0, e.Data.Length);

                // 处理不同类型的消息
                await HandleReceivedMessage(e.Connection.Id, message);
            }
            else
            {
                // 处理原始文本消息
                var text = Encoding.UTF8.GetString(e.Data);
                _logger?.LogInformation("收到文本数据 ({Length} 字节): {Text}", e.Data.Length, text);
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到文本 ({e.Data.Length} 字节): {text}");

                // 回显消息
                var response = DemoMessage.CreateResponseMessage(
                    DemoMessage.CreateTextMessage(text),
                    $"Echo: {text}",
                    DataGenerator.GenerateServerId()
                );
                await _tcpServer!.SendTextAsync(e.Connection.Id, response.ToJson());
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理接收数据时发生错误");
            _performanceMonitor?.RecordOperation(false);
        }
    }

    /// <summary>
    /// 数据发送事件处理
    /// </summary>
    private static void OnDataSent(object? sender, Liam.TcpServer.Events.DataSentEventArgs e)
    {
        _performanceMonitor?.RecordOperation(true, 0, e.Data.Length);
    }

    /// <summary>
    /// 错误事件处理
    /// </summary>
    private static void OnError(object? sender, Liam.TcpServer.Events.TcpServerErrorEventArgs e)
    {
        var message = $"服务器错误: {e.Exception.Message}";
        if (e.Connection != null)
        {
            message += $" (连接ID: {e.Connection.Id})";
        }

        _logger?.LogError(e.Exception, message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
        _performanceMonitor?.RecordOperation(false);
    }

    /// <summary>
    /// 心跳事件处理
    /// </summary>
    private static void OnHeartbeat(object? sender, Liam.TcpServer.Events.HeartbeatEventArgs e)
    {
        var message = $"心跳{(e.IsRequest ? "请求" : "响应")}: {e.Connection.Id}";
        _logger?.LogDebug(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 状态变更事件处理
    /// </summary>
    private static void OnStatusChanged(object? sender, Liam.TcpServer.Events.ServerStatusChangedEventArgs e)
    {
        var message = $"服务器状态变更: {e.OldStatus} -> {e.NewStatus}";
        _logger?.LogInformation(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 处理接收到的消息
    /// </summary>
    /// <param name="connectionId">连接ID</param>
    /// <param name="message">消息</param>
    private static async Task HandleReceivedMessage(string connectionId, DemoMessage message)
    {
        try
        {
            switch (message.Type)
            {
                case DemoMessageType.Text:
                    await HandleTextMessage(connectionId, message);
                    break;
                case DemoMessageType.Command:
                    await HandleCommandMessage(connectionId, message);
                    break;
                case DemoMessageType.Heartbeat:
                    await HandleHeartbeatMessage(connectionId, message);
                    break;
                case DemoMessageType.PerformanceTest:
                    await HandlePerformanceTestMessage(connectionId, message);
                    break;
                case DemoMessageType.FileTransfer:
                    await HandleFileTransferMessage(connectionId, message);
                    break;
                default:
                    var response = DemoMessage.CreateResponseMessage(message, "消息类型不支持", DataGenerator.GenerateServerId());
                    await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理消息时发生错误");
            var errorResponse = DemoMessage.CreateErrorMessage($"处理消息时发生错误: {ex.Message}", DataGenerator.GenerateServerId(), connectionId);
            await _tcpServer!.SendTextAsync(connectionId, errorResponse.ToJson());
        }
    }

    /// <summary>
    /// 处理文本消息
    /// </summary>
    private static async Task HandleTextMessage(string connectionId, DemoMessage message)
    {
        var response = DemoMessage.CreateResponseMessage(message, $"Echo: {message.Content}", DataGenerator.GenerateServerId());
        await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
    }

    /// <summary>
    /// 处理命令消息
    /// </summary>
    private static async Task HandleCommandMessage(string connectionId, DemoMessage message)
    {
        var command = message.Content?.ToLower() ?? "";
        var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var cmd = parts.Length > 0 ? parts[0] : "";

        string responseContent = cmd switch
        {
            "ping" => "pong",
            "time" => $"服务器时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
            "status" => GetServerStatus(),
            "stats" => GetServerStatistics(),
            "help" => GetHelpText(),
            "version" => "Liam.TcpServer Demo v1.0.0",
            "info" => GetConnectionInfo(connectionId),
            _ => $"未知命令: {cmd}，输入 'help' 查看可用命令"
        };

        var response = DemoMessage.CreateResponseMessage(message, responseContent, DataGenerator.GenerateServerId());
        await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
    }

    /// <summary>
    /// 处理心跳消息
    /// </summary>
    private static async Task HandleHeartbeatMessage(string connectionId, DemoMessage message)
    {
        var response = DemoMessage.CreateHeartbeatMessage(DataGenerator.GenerateServerId(), connectionId);
        response.Content = "pong";
        await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
    }

    /// <summary>
    /// 处理性能测试消息
    /// </summary>
    private static async Task HandlePerformanceTestMessage(string connectionId, DemoMessage message)
    {
        // 记录性能测试数据
        if (message.Properties?.TryGetValue("SequenceNumber", out var seqObj) == true && seqObj is int seq)
        {
            var response = DemoMessage.CreateResponseMessage(message, $"Performance test response #{seq}", DataGenerator.GenerateServerId());
            response.Properties = new Dictionary<string, object>
            {
                ["OriginalSequence"] = seq,
                ["ProcessedAt"] = DateTime.UtcNow,
                ["ServerLoad"] = _performanceMonitor?.GetRealTimeStats() ?? "Unknown"
            };
            await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
        }
    }

    /// <summary>
    /// 处理文件传输消息
    /// </summary>
    private static async Task HandleFileTransferMessage(string connectionId, DemoMessage message)
    {
        var dataSize = message.Data?.Length ?? 0;
        var checksum = message.Data != null ? DataGenerator.CalculateChecksum(message.Data) : 0;

        var response = DemoMessage.CreateResponseMessage(message,
            $"文件数据已接收，大小: {dataSize} 字节，校验和: {checksum}",
            DataGenerator.GenerateServerId());

        response.Properties = new Dictionary<string, object>
        {
            ["ReceivedSize"] = dataSize,
            ["Checksum"] = checksum,
            ["ProcessedAt"] = DateTime.UtcNow
        };

        await _tcpServer!.SendTextAsync(connectionId, response.ToJson());
    }

    /// <summary>
    /// 获取服务器状态
    /// </summary>
    private static string GetServerStatus()
    {
        if (_tcpServer == null) return "服务器未初始化";

        return $"""
            服务器状态: {_tcpServer.Status}
            运行状态: {(_tcpServer.IsRunning ? "运行中" : "已停止")}
            当前连接数: {_tcpServer.ConnectionCount}
            启动时间: {_tcpServer.StartedAt:yyyy-MM-dd HH:mm:ss}
            运行时间: {_tcpServer.Uptime?.ToString(@"dd\.hh\:mm\:ss") ?? "未知"}
            监听端口: {_tcpServer.Configuration.Port}
            最大连接数: {_tcpServer.Configuration.MaxConnections}
            """;
    }

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    private static string GetServerStatistics()
    {
        if (_tcpServer == null) return "服务器未初始化";

        var stats = _tcpServer.GetStatistics();
        return $"""
            === 服务器统计信息 ===
            总连接数: {stats.TotalConnections:N0}
            当前连接数: {stats.CurrentConnections}
            最大并发连接数: {stats.MaxConcurrentConnections}
            总发送字节数: {stats.TotalBytesSent:N0}
            总接收字节数: {stats.TotalBytesReceived:N0}
            总发送消息数: {stats.TotalMessagesSent:N0}
            总接收消息数: {stats.TotalMessagesReceived:N0}
            总错误数: {stats.TotalErrors:N0}
            错误率: {stats.ErrorRate:P2}
            平均发送速度: {stats.AverageSendRate / 1024.0 / 1024.0:F2} MB/s
            平均接收速度: {stats.AverageReceiveRate / 1024.0 / 1024.0:F2} MB/s
            """;
    }

    /// <summary>
    /// 获取连接信息
    /// </summary>
    private static string GetConnectionInfo(string connectionId)
    {
        if (_tcpServer == null) return "服务器未初始化";

        var connection = _tcpServer.GetConnection(connectionId);
        if (connection == null) return "连接不存在";

        return $"""
            === 连接信息 ===
            连接ID: {connection.Id}
            客户端地址: {connection.ClientIpAddress}:{connection.ClientPort}
            连接时间: {connection.ConnectedAt:yyyy-MM-dd HH:mm:ss}
            连接持续时间: {connection.ConnectionDuration:hh\:mm\:ss}
            发送字节数: {connection.Statistics.BytesSent:N0}
            接收字节数: {connection.Statistics.BytesReceived:N0}
            发送消息数: {connection.Statistics.MessagesSent:N0}
            接收消息数: {connection.Statistics.MessagesReceived:N0}
            """;
    }

    /// <summary>
    /// 获取帮助文本
    /// </summary>
    private static string GetHelpText()
    {
        return """
            === 可用命令 ===
            ping        - 测试连接
            time        - 获取服务器时间
            status      - 获取服务器状态
            stats       - 获取统计信息
            info        - 获取连接信息
            version     - 获取版本信息
            help        - 显示此帮助信息
            """;
    }

    /// <summary>
    /// 显示服务器信息
    /// </summary>
    private static void DisplayServerInfo()
    {
        if (_tcpServer == null) return;

        Console.WriteLine($"服务器配置:");
        Console.WriteLine($"  监听端口: {_tcpServer.Configuration.Port}");
        Console.WriteLine($"  最大连接数: {_tcpServer.Configuration.MaxConnections}");
        Console.WriteLine($"  心跳检测: {(_tcpServer.Configuration.EnableHeartbeat ? "启用" : "禁用")}");
        Console.WriteLine($"  SSL/TLS: {(_tcpServer.Configuration.EnableSsl ? "启用" : "禁用")}");
        Console.WriteLine($"  缓冲区大小: {_tcpServer.Configuration.BufferSize} 字节");
        Console.WriteLine();
    }

    /// <summary>
    /// 显示可用命令
    /// </summary>
    private static void DisplayCommands()
    {
        Console.WriteLine("=== 服务器控制台命令 ===");
        Console.WriteLine("  /help          - 显示帮助信息");
        Console.WriteLine("  /status        - 显示服务器状态");
        Console.WriteLine("  /stats         - 显示统计信息");
        Console.WriteLine("  /clients       - 显示连接的客户端");
        Console.WriteLine("  /broadcast <消息> - 广播消息到所有客户端");
        Console.WriteLine("  /test <场景>   - 运行测试场景");
        Console.WriteLine("  /monitor       - 显示实时监控信息");
        Console.WriteLine("  /clear         - 清屏");
        Console.WriteLine("  /quit          - 停止服务器");
        Console.WriteLine();
        Console.WriteLine("等待客户端连接...");
        Console.WriteLine("提示: 可以使用Liam.TcpClient测试应用或telnet连接测试");
        Console.WriteLine();
    }

    /// <summary>
    /// 处理控制台输入
    /// </summary>
    private static async Task HandleConsoleInput()
    {
        while (_tcpServer?.IsRunning == true)
        {
            Console.Write("> ");
            var input = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(input))
                continue;

            var parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                continue;

            var command = parts[0].ToLower();

            try
            {
                switch (command)
                {
                    case "/help":
                        DisplayCommands();
                        break;

                    case "/status":
                        Console.WriteLine(GetServerStatus());
                        break;

                    case "/stats":
                        Console.WriteLine(GetServerStatistics());
                        if (_performanceMonitor != null)
                        {
                            Console.WriteLine("\n=== 性能监控信息 ===");
                            Console.WriteLine(_performanceMonitor.GetRealTimeStats());
                        }
                        break;

                    case "/clients":
                        await DisplayActiveClients();
                        break;

                    case "/broadcast":
                        if (parts.Length > 1)
                        {
                            var message = string.Join(" ", parts.Skip(1));
                            await BroadcastMessage(message);
                        }
                        else
                        {
                            Console.WriteLine("用法: /broadcast <消息>");
                        }
                        break;

                    case "/test":
                        if (parts.Length > 1)
                        {
                            await RunTestScenario(parts[1]);
                        }
                        else
                        {
                            DisplayAvailableTests();
                        }
                        break;

                    case "/monitor":
                        await DisplayMonitorInfo();
                        break;

                    case "/clear":
                        Console.Clear();
                        DisplayServerInfo();
                        DisplayCommands();
                        break;

                    case "/quit":
                        Console.WriteLine("正在停止服务器...");
                        return;

                    default:
                        Console.WriteLine($"未知命令: {command}，输入 /help 查看可用命令");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"执行命令时发生错误: {ex.Message}");
                _logger?.LogError(ex, "执行控制台命令时发生错误: {Command}", command);
            }
        }
    }

    /// <summary>
    /// 显示活跃客户端
    /// </summary>
    private static async Task DisplayActiveClients()
    {
        if (_tcpServer == null) return;

        var connections = _tcpServer.GetActiveConnections();
        Console.WriteLine($"\n=== 活跃连接 ({connections.Count}) ===");

        if (connections.Count == 0)
        {
            Console.WriteLine("无活跃连接");
        }
        else
        {
            foreach (var conn in connections)
            {
                Console.WriteLine($"  {conn.GetConnectionSummary()}");
            }
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 广播消息
    /// </summary>
    private static async Task BroadcastMessage(string message)
    {
        if (_tcpServer == null) return;

        var broadcastMsg = DemoMessage.CreateTextMessage(
            $"[服务器广播] {message}",
            DataGenerator.GenerateServerId()
        );

        var count = await _tcpServer.BroadcastTextAsync(broadcastMsg.ToJson());
        Console.WriteLine($"消息已广播到 {count} 个客户端");
    }

    /// <summary>
    /// 显示可用测试
    /// </summary>
    private static void DisplayAvailableTests()
    {
        Console.WriteLine("\n=== 可用测试场景 ===");
        Console.WriteLine("  basic       - 基本连接测试");
        Console.WriteLine("  message     - 消息交换测试");
        Console.WriteLine("  heartbeat   - 心跳检测测试");
        Console.WriteLine("  performance - 性能压力测试");
        Console.WriteLine("  large       - 大数据传输测试");
        Console.WriteLine("\n用法: /test <场景名称>");
    }

    /// <summary>
    /// 运行测试场景
    /// </summary>
    private static async Task RunTestScenario(string scenarioName)
    {
        TestScenario? scenario = scenarioName.ToLower() switch
        {
            "basic" => TestScenario.CreateBasicConnectionTest(),
            "message" => TestScenario.CreateMessageExchangeTest(),
            "heartbeat" => TestScenario.CreateHeartbeatTest(),
            "performance" => TestScenario.CreatePerformanceStressTest(),
            "large" => TestScenario.CreateLargeDataTransferTest(),
            _ => null
        };

        if (scenario == null)
        {
            Console.WriteLine($"未知测试场景: {scenarioName}");
            DisplayAvailableTests();
            return;
        }

        Console.WriteLine($"正在运行测试场景: {scenario.Name}");
        Console.WriteLine($"描述: {scenario.Description}");

        _activeScenarios[scenario.Id] = scenario;

        // 这里可以添加具体的测试逻辑
        // 目前只是模拟测试运行
        await Task.Delay(1000);
        Console.WriteLine("测试场景已启动，等待客户端连接进行测试...");
    }

    /// <summary>
    /// 显示监控信息
    /// </summary>
    private static async Task DisplayMonitorInfo()
    {
        if (_performanceMonitor == null) return;

        Console.WriteLine("\n=== 实时监控信息 ===");
        Console.WriteLine(_performanceMonitor.GetRealTimeStats());

        if (_tcpServer != null)
        {
            var stats = _tcpServer.GetStatistics();
            Console.WriteLine($"连接数: {stats.CurrentConnections}/{_tcpServer.Configuration.MaxConnections}");
            Console.WriteLine($"总连接数: {stats.TotalConnections}");
            Console.WriteLine($"错误率: {stats.ErrorRate:P2}");
        }

        Console.WriteLine($"内存使用: {_performanceMonitor.GetCurrentMemoryUsage() / 1024.0 / 1024.0:F2} MB");
        Console.WriteLine($"CPU使用率: {_performanceMonitor.GetCpuUsage():F1}%");
        Console.WriteLine();
    }

    /// <summary>
    /// 服务器配置类
    /// </summary>
    private class ServerConfig
    {
        public int Port { get; set; } = 8888;
        public int MaxConnections { get; set; } = 100;
        public bool EnableHeartbeat { get; set; } = false;
        public int HeartbeatInterval { get; set; } = 30;
        public int HeartbeatTimeout { get; set; } = 60;
        public bool EnableSsl { get; set; } = false;
        public int BufferSize { get; set; } = 4096;
    }
}
