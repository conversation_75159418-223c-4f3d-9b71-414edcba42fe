# Liam.TcpClient & Liam.TcpServer 完整功能测试应用

这是一个完整的TCP通信测试应用程序，用于验证和演示Liam.TcpClient和Liam.TcpServer库的所有功能特性。

## 项目结构

```
TcpDemo/
├── TcpDemo.Shared/          # 共享模型和工具类
│   ├── Models/              # 消息模型、测试场景、测试结果
│   └── Utils/               # 性能监控、数据生成器
├── TcpDemo.Server/          # TCP服务器测试应用
├── TcpDemo.Client/          # TCP客户端测试应用
└── TcpDemo.Tests/           # 集成测试项目
```

## 功能特性

### 🚀 核心功能测试
- ✅ **基本TCP连接建立和断开**
- ✅ **同步和异步消息发送/接收**
- ✅ **心跳检测和自动重连**
- ✅ **异常处理和错误恢复**
- ✅ **连接质量监控和统计**

### 📊 性能测试
- ✅ **并发连接测试**（多客户端同时连接）
- ✅ **大数据传输测试**（MB级数据传输）
- ✅ **长连接稳定性测试**
- ✅ **性能压力测试**（高频消息发送）
- ✅ **连接池管理和复用**

### 🔒 高级功能
- 🔄 **SSL/TLS安全连接测试**（开发中）
- ✅ **实时性能监控**
- ✅ **详细统计信息**
- ✅ **自定义消息协议**

## 快速开始

### 1. 构建项目

```bash
# 在项目根目录执行
dotnet build examples/TcpDemo/TcpDemo.Server
dotnet build examples/TcpDemo/TcpDemo.Client
```

### 2. 启动服务器

```bash
# 基本启动
dotnet run --project examples/TcpDemo/TcpDemo.Server

# 自定义配置启动
dotnet run --project examples/TcpDemo/TcpDemo.Server -- --port 9999 --max-connections 50 --enable-heartbeat
```

**服务器命令行参数：**
- `--port, -p <端口>`：服务器监听端口（默认：8888）
- `--max-connections, -mc <数量>`：最大连接数（默认：100）
- `--enable-heartbeat, -hb`：启用心跳检测
- `--enable-ssl, -ssl`：启用SSL/TLS
- `--buffer-size, -bs <大小>`：缓冲区大小（默认：4096）

### 3. 启动客户端

```bash
# 基本启动
dotnet run --project examples/TcpDemo/TcpDemo.Client

# 自定义配置启动
dotnet run --project examples/TcpDemo/TcpDemo.Client -- --host localhost --port 9999 --auto-reconnect --enable-heartbeat
```

**客户端命令行参数：**
- `--host, -h <主机>`：服务器主机地址（默认：localhost）
- `--port, -p <端口>`：服务器端口（默认：8888）
- `--auto-reconnect, -ar`：启用自动重连
- `--enable-heartbeat, -hb`：启用心跳检测
- `--enable-ssl, -ssl`：启用SSL/TLS
- `--timeout, -t <秒数>`：连接超时时间（默认：30）

## 使用指南

### 服务器控制台命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `/help` | 显示帮助信息 | `/help` |
| `/status` | 显示服务器状态 | `/status` |
| `/stats` | 显示统计信息 | `/stats` |
| `/clients` | 显示连接的客户端 | `/clients` |
| `/broadcast <消息>` | 广播消息到所有客户端 | `/broadcast Hello World` |
| `/test <场景>` | 运行测试场景 | `/test performance` |
| `/monitor` | 显示实时监控信息 | `/monitor` |
| `/clear` | 清屏 | `/clear` |
| `/quit` | 停止服务器 | `/quit` |

### 客户端控制台命令

| 命令 | 描述 | 示例 |
|------|------|------|
| `/connect` | 连接到服务器 | `/connect` |
| `/disconnect` | 断开连接 | `/disconnect` |
| `/reconnect` | 重新连接 | `/reconnect` |
| `/send <消息>` | 发送文本消息 | `/send Hello Server` |
| `/command <命令>` | 发送命令消息 | `/command ping` |
| `/ping` | 测试连接延迟 | `/ping` |
| `/status` | 显示连接状态 | `/status` |
| `/stats` | 显示统计信息 | `/stats` |
| `/test <场景>` | 运行测试场景 | `/test basic` |
| `/monitor` | 显示实时监控信息 | `/monitor` |
| `/quit` | 退出客户端 | `/quit` |

### 可用测试场景

| 场景名称 | 描述 | 用法 |
|----------|------|------|
| `basic` | 基本连接测试 | `/test basic` |
| `message` | 消息交换测试 | `/test message` |
| `heartbeat` | 心跳检测测试 | `/test heartbeat` |
| `performance` | 性能压力测试 | `/test performance` |
| `large` | 大数据传输测试 | `/test large` |
| `reconnect` | 自动重连测试 | `/test reconnect` |

## 测试场景详解

### 1. 基本连接测试
- 验证TCP连接的建立和断开
- 测试基本的消息发送和接收
- 检查连接状态的正确性

### 2. 消息交换测试
- 发送指定数量的测试消息
- 验证消息的完整性和顺序
- 测试不同大小的消息传输

### 3. 性能压力测试
- 高频率发送大量消息
- 测试系统在高负载下的表现
- 监控吞吐量、延迟和错误率

### 4. 大数据传输测试
- 传输MB级别的大数据包
- 验证数据完整性
- 测试传输速度和稳定性

### 5. 并发连接测试
- 多个客户端同时连接服务器
- 测试服务器的并发处理能力
- 验证连接池的管理效果

### 6. 长连接稳定性测试
- 保持长时间连接
- 测试连接的稳定性
- 验证内存泄漏和资源管理

## 运行集成测试

```bash
# 运行所有测试
dotnet test examples/TcpDemo/TcpDemo.Tests

# 运行特定测试
dotnet test examples/TcpDemo/TcpDemo.Tests --filter "BasicConnection"

# 详细输出
dotnet test examples/TcpDemo/TcpDemo.Tests --verbosity normal
```

## 性能监控

应用程序提供了详细的性能监控功能：

### 实时监控指标
- **连接数统计**：当前连接数、总连接数、最大并发数
- **数据传输统计**：发送/接收字节数、消息数
- **性能指标**：吞吐量、延迟、错误率
- **系统资源**：内存使用、CPU使用率

### 统计信息
- **成功率**：操作成功的百分比
- **平均延迟**：消息往返时间
- **传输速度**：数据传输速率（MB/s）
- **连接质量**：连接稳定性评分

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器是否已启动
   - 确认端口号是否正确
   - 检查防火墙设置

2. **消息发送失败**
   - 确认客户端已连接
   - 检查网络连接状态
   - 查看错误日志

3. **性能问题**
   - 调整缓冲区大小
   - 检查系统资源使用
   - 优化消息发送频率

### 日志文件

应用程序会在 `logs/` 目录下生成详细的日志文件：
- `server-{date}.log`：服务器日志
- `client-{date}.log`：客户端日志

## 扩展开发

### 添加自定义测试场景

1. 在 `TcpDemo.Shared/Models/TestScenario.cs` 中添加新的场景类型
2. 在客户端或服务器中实现对应的测试逻辑
3. 更新命令处理器以支持新场景

### 自定义消息类型

1. 在 `TcpDemo.Shared/Models/DemoMessage.cs` 中添加新的消息类型
2. 实现相应的序列化/反序列化逻辑
3. 在服务器和客户端中添加处理逻辑

## 技术规范

- **.NET版本**：.NET 8.0
- **依赖库**：Liam.TcpServer、Liam.TcpClient、Liam.Logging
- **测试框架**：xUnit、Moq、FluentAssertions
- **日志框架**：Microsoft.Extensions.Logging
- **序列化**：System.Text.Json

## 许可证

本项目遵循MIT许可证。详见LICENSE文件。
