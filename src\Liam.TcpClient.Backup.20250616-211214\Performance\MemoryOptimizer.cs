using System.Buffers;
using System.Runtime.CompilerServices;

namespace Liam.TcpClient.Performance;

/// <summary>
/// 高性能内存操作优化器
/// </summary>
public static class MemoryOptimizer
{
    private static readonly ArrayPool<byte> _bytePool = ArrayPool<byte>.Shared;
    private static readonly ArrayPool<char> _charPool = ArrayPool<char>.Shared;
    private static readonly int _defaultBufferSize = 4096;
    private static readonly int _largeBufferThreshold = 85000; // LOH阈值

    /// <summary>
    /// 租用字节缓冲区
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>缓冲区包装器</returns>
    public static PooledByteBuffer RentByteBuffer(int minimumLength)
    {
        var buffer = _bytePool.Rent(minimumLength);
        return new PooledByteBuffer(buffer, minimumLength);
    }

    /// <summary>
    /// 租用字符缓冲区
    /// </summary>
    /// <param name="minimumLength">最小长度</param>
    /// <returns>缓冲区包装器</returns>
    public static PooledCharBuffer RentCharBuffer(int minimumLength)
    {
        var buffer = _charPool.Rent(minimumLength);
        return new PooledCharBuffer(buffer, minimumLength);
    }

    /// <summary>
    /// 高效复制内存
    /// </summary>
    /// <param name="source">源</param>
    /// <param name="destination">目标</param>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static void CopyMemory(ReadOnlySpan<byte> source, Span<byte> destination)
    {
        if (source.Length <= destination.Length)
        {
            source.CopyTo(destination);
        }
        else
        {
            throw new ArgumentException("目标缓冲区太小");
        }
    }

    /// <summary>
    /// 安全的内存复制，返回实际复制的字节数
    /// </summary>
    /// <param name="source">源</param>
    /// <param name="destination">目标</param>
    /// <returns>实际复制的字节数</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int SafeCopyMemory(ReadOnlySpan<byte> source, Span<byte> destination)
    {
        var copyLength = Math.Min(source.Length, destination.Length);
        source.Slice(0, copyLength).CopyTo(destination);
        return copyLength;
    }

    /// <summary>
    /// 检查是否会分配到大对象堆
    /// </summary>
    /// <param name="size">大小</param>
    /// <returns>是否为大对象</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static bool IsLargeObject(int size)
    {
        return size >= _largeBufferThreshold;
    }

    /// <summary>
    /// 获取推荐的缓冲区大小
    /// </summary>
    /// <param name="requestedSize">请求的大小</param>
    /// <returns>推荐的缓冲区大小</returns>
    public static int GetRecommendedBufferSize(int requestedSize)
    {
        if (requestedSize <= 0)
        {
            return _defaultBufferSize;
        }

        // 避免大对象堆分配
        if (requestedSize >= _largeBufferThreshold)
        {
            return _largeBufferThreshold - 1;
        }

        // 向上舍入到2的幂次
        var size = 1;
        while (size < requestedSize)
        {
            size <<= 1;
        }

        return Math.Min(size, _largeBufferThreshold - 1);
    }

    /// <summary>
    /// 创建内存所有者
    /// </summary>
    /// <param name="size">大小</param>
    /// <returns>内存所有者</returns>
    public static IMemoryOwner<byte> CreateMemoryOwner(int size)
    {
        return MemoryPool<byte>.Shared.Rent(size);
    }

    /// <summary>
    /// 高效的字节数组比较
    /// </summary>
    /// <param name="left">左侧数组</param>
    /// <param name="right">右侧数组</param>
    /// <returns>是否相等</returns>
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static bool SequenceEqual(ReadOnlySpan<byte> left, ReadOnlySpan<byte> right)
    {
        return left.SequenceEqual(right);
    }

    /// <summary>
    /// 零分配的字节转十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <param name="destination">目标字符缓冲区</param>
    /// <returns>是否成功</returns>
    public static bool TryToHexString(ReadOnlySpan<byte> bytes, Span<char> destination)
    {
        if (destination.Length < bytes.Length * 2)
        {
            return false;
        }

        const string hexChars = "0123456789ABCDEF";
        for (int i = 0; i < bytes.Length; i++)
        {
            var b = bytes[i];
            destination[i * 2] = hexChars[b >> 4];
            destination[i * 2 + 1] = hexChars[b & 0xF];
        }

        return true;
    }
}

/// <summary>
/// 池化字节缓冲区包装器
/// </summary>
public readonly struct PooledByteBuffer : IDisposable
{
    private readonly byte[] _buffer;
    private readonly int _length;

    /// <summary>
    /// 初始化池化字节缓冲区
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="length">有效长度</param>
    internal PooledByteBuffer(byte[] buffer, int length)
    {
        _buffer = buffer;
        _length = length;
    }

    /// <summary>
    /// 获取缓冲区
    /// </summary>
    public byte[] Buffer => _buffer;

    /// <summary>
    /// 获取有效长度
    /// </summary>
    public int Length => _length;

    /// <summary>
    /// 获取内存跨度
    /// </summary>
    public Span<byte> Span => _buffer.AsSpan(0, _length);

    /// <summary>
    /// 获取只读内存跨度
    /// </summary>
    public ReadOnlySpan<byte> ReadOnlySpan => _buffer.AsSpan(0, _length);

    /// <summary>
    /// 释放缓冲区
    /// </summary>
    public void Dispose()
    {
        if (_buffer != null)
        {
            ArrayPool<byte>.Shared.Return(_buffer);
        }
    }
}

/// <summary>
/// 池化字符缓冲区包装器
/// </summary>
public readonly struct PooledCharBuffer : IDisposable
{
    private readonly char[] _buffer;
    private readonly int _length;

    /// <summary>
    /// 初始化池化字符缓冲区
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="length">有效长度</param>
    internal PooledCharBuffer(char[] buffer, int length)
    {
        _buffer = buffer;
        _length = length;
    }

    /// <summary>
    /// 获取缓冲区
    /// </summary>
    public char[] Buffer => _buffer;

    /// <summary>
    /// 获取有效长度
    /// </summary>
    public int Length => _length;

    /// <summary>
    /// 获取内存跨度
    /// </summary>
    public Span<char> Span => _buffer.AsSpan(0, _length);

    /// <summary>
    /// 获取只读内存跨度
    /// </summary>
    public ReadOnlySpan<char> ReadOnlySpan => _buffer.AsSpan(0, _length);

    /// <summary>
    /// 释放缓冲区
    /// </summary>
    public void Dispose()
    {
        if (_buffer != null)
        {
            ArrayPool<char>.Shared.Return(_buffer);
        }
    }
}

/// <summary>
/// 高性能批量操作器
/// </summary>
public static class BatchOptimizer
{
    /// <summary>
    /// 高效的批量数据处理
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="items">数据项</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="processor">处理器</param>
    /// <param name="maxConcurrency">最大并发数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    public static async Task ProcessBatchAsync<T>(
        IEnumerable<T> items,
        int batchSize,
        Func<IEnumerable<T>, CancellationToken, Task> processor,
        int maxConcurrency = 4,
        CancellationToken cancellationToken = default)
    {
        using var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        var batches = items.Chunk(batchSize);
        var tasks = new List<Task>();

        foreach (var batch in batches)
        {
            await semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            
            var task = Task.Run(async () =>
            {
                try
                {
                    await processor(batch, cancellationToken).ConfigureAwait(false);
                }
                finally
                {
                    semaphore.Release();
                }
            }, cancellationToken);

            tasks.Add(task);
        }

        await Task.WhenAll(tasks).ConfigureAwait(false);
    }

    /// <summary>
    /// 高效的批量数据处理（带返回值）
    /// </summary>
    /// <typeparam name="TInput">输入类型</typeparam>
    /// <typeparam name="TOutput">输出类型</typeparam>
    /// <param name="items">数据项</param>
    /// <param name="batchSize">批次大小</param>
    /// <param name="processor">处理器</param>
    /// <param name="maxConcurrency">最大并发数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    public static async Task<IEnumerable<TOutput>> ProcessBatchAsync<TInput, TOutput>(
        IEnumerable<TInput> items,
        int batchSize,
        Func<IEnumerable<TInput>, CancellationToken, Task<IEnumerable<TOutput>>> processor,
        int maxConcurrency = 4,
        CancellationToken cancellationToken = default)
    {
        using var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        var batches = items.Chunk(batchSize);
        var tasks = new List<Task<IEnumerable<TOutput>>>();

        foreach (var batch in batches)
        {
            await semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);
            
            var task = Task.Run(async () =>
            {
                try
                {
                    return await processor(batch, cancellationToken).ConfigureAwait(false);
                }
                finally
                {
                    semaphore.Release();
                }
            }, cancellationToken);

            tasks.Add(task);
        }

        var results = await Task.WhenAll(tasks).ConfigureAwait(false);
        return results.SelectMany(r => r);
    }
}
