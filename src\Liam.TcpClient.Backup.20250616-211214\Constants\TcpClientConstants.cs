namespace Liam.TcpClient.Constants;

/// <summary>
/// TCP客户端相关常量定义
/// </summary>
public static class TcpClientConstants
{
    /// <summary>
    /// 默认配置常量
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认服务器端口号
        /// </summary>
        public const int Port = 8080;

        /// <summary>
        /// 默认连接超时时间 (30秒)
        /// </summary>
        public const int ConnectionTimeoutSeconds = 30;

        /// <summary>
        /// 默认接收缓冲区大小 (8KB)
        /// </summary>
        public const int ReceiveBufferSize = 8192;

        /// <summary>
        /// 默认发送缓冲区大小 (8KB)
        /// </summary>
        public const int SendBufferSize = 8192;

        /// <summary>
        /// 默认心跳间隔 (60秒)
        /// </summary>
        public const int HeartbeatIntervalSeconds = 60;

        /// <summary>
        /// 默认心跳超时时间 (10秒)
        /// </summary>
        public const int HeartbeatTimeoutSeconds = 10;

        /// <summary>
        /// 默认消息最大长度 (1MB)
        /// </summary>
        public const int MaxMessageLength = 1024 * 1024;

        /// <summary>
        /// 默认重连间隔 (5秒)
        /// </summary>
        public const int ReconnectIntervalSeconds = 5;

        /// <summary>
        /// 默认最大重连次数
        /// </summary>
        public const int MaxReconnectAttempts = 10;

        /// <summary>
        /// 默认连接池大小
        /// </summary>
        public const int ConnectionPoolSize = 10;
    }

    /// <summary>
    /// 消息协议常量
    /// </summary>
    public static class Protocol
    {
        /// <summary>
        /// 消息头大小 (1字节类型 + 4字节长度)
        /// </summary>
        public const int MessageHeaderSize = 5;
    }

    /// <summary>
    /// 消息类型常量 (与TcpServer保持一致)
    /// </summary>
    public static class MessageTypes
    {
        /// <summary>
        /// 普通数据消息
        /// </summary>
        public const byte Data = 0x01;

        /// <summary>
        /// 心跳请求消息
        /// </summary>
        public const byte HeartbeatRequest = 0x02;

        /// <summary>
        /// 心跳响应消息
        /// </summary>
        public const byte HeartbeatResponse = 0x03;

        /// <summary>
        /// 连接确认消息
        /// </summary>
        public const byte ConnectionAck = 0x04;

        /// <summary>
        /// 断开连接消息
        /// </summary>
        public const byte Disconnect = 0x05;

        /// <summary>
        /// 错误消息
        /// </summary>
        public const byte Error = 0xFF;
    }

    /// <summary>
    /// 连接状态常量
    /// </summary>
    public static class ConnectionStates
    {
        /// <summary>
        /// 未连接
        /// </summary>
        public const string Disconnected = "Disconnected";

        /// <summary>
        /// 连接中
        /// </summary>
        public const string Connecting = "Connecting";

        /// <summary>
        /// 已连接
        /// </summary>
        public const string Connected = "Connected";

        /// <summary>
        /// 重连中
        /// </summary>
        public const string Reconnecting = "Reconnecting";

        /// <summary>
        /// 断开连接中
        /// </summary>
        public const string Disconnecting = "Disconnecting";

        /// <summary>
        /// 错误状态
        /// </summary>
        public const string Error = "Error";
    }

    /// <summary>
    /// 安全相关常量
    /// </summary>
    public static class Security
    {
        /// <summary>
        /// 默认SSL握手超时时间 (秒)
        /// </summary>
        public const int SslHandshakeTimeoutSeconds = 30;

        /// <summary>
        /// 默认证书验证超时时间 (秒)
        /// </summary>
        public const int CertificateValidationTimeoutSeconds = 10;
    }

    /// <summary>
    /// 性能监控相关常量
    /// </summary>
    public static class Performance
    {
        /// <summary>
        /// 统计信息更新间隔 (秒)
        /// </summary>
        public const int StatisticsUpdateIntervalSeconds = 10;

        /// <summary>
        /// 性能计数器采样间隔 (毫秒)
        /// </summary>
        public const int PerformanceCounterSampleIntervalMs = 1000;

        /// <summary>
        /// 内存池最大对象数量
        /// </summary>
        public const int MemoryPoolMaxObjects = 100;

        /// <summary>
        /// StringBuilder 初始容量
        /// </summary>
        public const int StringBuilderInitialCapacity = 256;
    }

    /// <summary>
    /// 错误消息常量
    /// </summary>
    public static class ErrorMessages
    {
        /// <summary>
        /// 客户端未连接
        /// </summary>
        public const string ClientNotConnected = "客户端未连接";

        /// <summary>
        /// 客户端已连接
        /// </summary>
        public const string ClientAlreadyConnected = "客户端已连接";

        /// <summary>
        /// 连接失败
        /// </summary>
        public const string ConnectionFailed = "连接失败";

        /// <summary>
        /// 连接超时
        /// </summary>
        public const string ConnectionTimeout = "连接超时";

        /// <summary>
        /// 消息长度无效
        /// </summary>
        public const string InvalidMessageLength = "消息长度无效";

        /// <summary>
        /// 心跳超时
        /// </summary>
        public const string HeartbeatTimeout = "心跳超时";

        /// <summary>
        /// 认证失败
        /// </summary>
        public const string AuthenticationFailed = "认证失败";

        /// <summary>
        /// 服务器断开连接
        /// </summary>
        public const string ServerDisconnected = "服务器断开连接";

        /// <summary>
        /// 重连失败
        /// </summary>
        public const string ReconnectFailed = "重连失败";

        /// <summary>
        /// SSL握手失败
        /// </summary>
        public const string SslHandshakeFailed = "SSL握手失败";
    }
}
