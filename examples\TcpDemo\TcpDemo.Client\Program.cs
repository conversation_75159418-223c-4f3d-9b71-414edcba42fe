﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Liam.Logging.Extensions;
using TcpDemo.Shared.Models;
using TcpDemo.Shared.Utils;

namespace TcpDemo.Client;

class Program
{
    private static ITcpClient? _tcpClient;
    private static ILogger<Program>? _logger;
    private static PerformanceMonitor? _performanceMonitor;
    private static readonly Dictionary<string, TestScenario> _activeScenarios = new();
    private static readonly Dictionary<string, TestResult> _testResults = new();
    private static string _clientId = DataGenerator.GenerateClientId();

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpClient 完整功能测试应用 ===\n");

        // 解析命令行参数
        var config = ParseArguments(args);

        // 创建主机构建器
        var builder = Host.CreateDefaultBuilder(args);

        // 配置服务
        builder.ConfigureServices((context, services) =>
        {
            // 添加Liam.Logging服务
            services.AddLiamLogging(options =>
            {
                options.MinimumLevel = LogLevel.Information;
                options.EnableConsoleLogging = true;
                options.EnableFileLogging = true;
                options.LogDirectory = "logs";
                options.EnableStructuredLogging = true;
            });

            // 添加TCP客户端服务
            services.AddTcpClient(clientConfig =>
            {
                clientConfig.ServerHost = config.ServerHost;
                clientConfig.ServerPort = config.ServerPort;
                clientConfig.EnableAutoReconnect = config.EnableAutoReconnect;
                clientConfig.ReconnectIntervalSeconds = config.ReconnectInterval;
                clientConfig.EnableHeartbeat = config.EnableHeartbeat;
                clientConfig.HeartbeatIntervalSeconds = config.HeartbeatInterval;
                clientConfig.HeartbeatTimeoutSeconds = config.HeartbeatTimeout;
                clientConfig.EnableSsl = config.EnableSsl;
                clientConfig.BufferSize = config.BufferSize;
                clientConfig.ConnectionTimeoutSeconds = config.ConnectionTimeout;
            });
        });

        // 构建主机
        var host = builder.Build();

        // 获取服务实例
        _tcpClient = host.Services.GetRequiredService<ITcpClient>();
        _logger = host.Services.GetRequiredService<ILogger<Program>>();
        _performanceMonitor = new PerformanceMonitor();

        // 订阅事件
        SubscribeToClientEvents();

        try
        {
            DisplayClientInfo();
            DisplayCommands();

            // 处理控制台输入
            await HandleConsoleInput();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "客户端运行时发生错误");
            Console.WriteLine($"客户端运行时发生错误: {ex.Message}");
        }
        finally
        {
            // 断开连接
            if (_tcpClient?.IsConnected == true)
            {
                _logger?.LogInformation("正在断开连接...");
                await _tcpClient.DisconnectAsync("客户端退出");
            }

            _performanceMonitor?.Stop();
            _performanceMonitor?.Dispose();
            _logger?.LogInformation("客户端已退出");
        }
    }

    /// <summary>
    /// 解析命令行参数
    /// </summary>
    /// <param name="args">命令行参数</param>
    /// <returns>客户端配置</returns>
    private static ClientConfig ParseArguments(string[] args)
    {
        var config = new ClientConfig();

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i].ToLower())
            {
                case "--host" or "-h":
                    if (i + 1 < args.Length)
                    {
                        config.ServerHost = args[i + 1];
                        i++;
                    }
                    break;
                case "--port" or "-p":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int port))
                    {
                        config.ServerPort = port;
                        i++;
                    }
                    break;
                case "--auto-reconnect" or "-ar":
                    config.EnableAutoReconnect = true;
                    break;
                case "--enable-heartbeat" or "-hb":
                    config.EnableHeartbeat = true;
                    break;
                case "--enable-ssl" or "-ssl":
                    config.EnableSsl = true;
                    break;
                case "--buffer-size" or "-bs":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int bufferSize))
                    {
                        config.BufferSize = bufferSize;
                        i++;
                    }
                    break;
                case "--timeout" or "-t":
                    if (i + 1 < args.Length && int.TryParse(args[i + 1], out int timeout))
                    {
                        config.ConnectionTimeout = timeout;
                        i++;
                    }
                    break;
                case "--help":
                    DisplayHelp();
                    Environment.Exit(0);
                    break;
            }
        }

        return config;
    }

    /// <summary>
    /// 显示帮助信息
    /// </summary>
    private static void DisplayHelp()
    {
        Console.WriteLine("Liam.TcpClient 测试应用程序");
        Console.WriteLine();
        Console.WriteLine("用法: TcpDemo.Client [选项]");
        Console.WriteLine();
        Console.WriteLine("选项:");
        Console.WriteLine("  -h, --host <主机>              服务器主机地址 (默认: localhost)");
        Console.WriteLine("  -p, --port <端口>              服务器端口 (默认: 8888)");
        Console.WriteLine("  -ar, --auto-reconnect          启用自动重连");
        Console.WriteLine("  -hb, --enable-heartbeat        启用心跳检测");
        Console.WriteLine("  -ssl, --enable-ssl             启用SSL/TLS");
        Console.WriteLine("  -bs, --buffer-size <大小>      缓冲区大小 (默认: 4096)");
        Console.WriteLine("  -t, --timeout <秒数>           连接超时时间 (默认: 30)");
        Console.WriteLine("  --help                         显示帮助信息");
    }

    /// <summary>
    /// 订阅客户端事件
    /// </summary>
    private static void SubscribeToClientEvents()
    {
        if (_tcpClient == null) return;

        _tcpClient.Connected += OnConnected;
        _tcpClient.Disconnected += OnDisconnected;
        _tcpClient.ConnectionStateChanged += OnConnectionStateChanged;
        _tcpClient.DataReceived += OnDataReceived;
        _tcpClient.DataSent += OnDataSent;
        _tcpClient.MessageReceived += OnMessageReceived;
        _tcpClient.MessageSent += OnMessageSent;
        _tcpClient.Heartbeat += OnHeartbeat;
        _tcpClient.Error += OnError;
    }

    /// <summary>
    /// 连接建立事件处理
    /// </summary>
    private static void OnConnected(object? sender, Liam.TcpClient.Events.ConnectedEventArgs e)
    {
        var message = $"已连接到服务器: {e.ServerHost}:{e.ServerPort}";
        _logger?.LogInformation(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
        Console.WriteLine($"    连接时间: {e.ConnectedAt:yyyy-MM-dd HH:mm:ss}");

        _performanceMonitor?.Start();
        _performanceMonitor?.RecordOperation(true);
    }

    /// <summary>
    /// 连接断开事件处理
    /// </summary>
    private static void OnDisconnected(object? sender, Liam.TcpClient.Events.DisconnectedEventArgs e)
    {
        var message = $"已断开连接: {e.Reason ?? "未指定原因"}";
        _logger?.LogInformation(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");

        if (e.ConnectionDuration.HasValue)
        {
            Console.WriteLine($"    连接持续时间: {e.ConnectionDuration.Value:hh\\:mm\\:ss}");
        }
    }

    /// <summary>
    /// 连接状态变更事件处理
    /// </summary>
    private static void OnConnectionStateChanged(object? sender, Liam.TcpClient.Events.ConnectionStateChangedEventArgs e)
    {
        var message = $"连接状态变更: {e.OldState} -> {e.NewState}";
        _logger?.LogDebug(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    private static void OnDataReceived(object? sender, Liam.TcpClient.Events.DataReceivedEventArgs e)
    {
        try
        {
            var jsonData = Encoding.UTF8.GetString(e.Data);
            var message = DemoMessage.FromJson(jsonData);

            if (message != null)
            {
                _logger?.LogInformation("收到消息: {MessageSummary}", message.GetSummary());
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到消息: {message.GetSummary()}");

                // 显示消息内容
                if (!string.IsNullOrEmpty(message.Content))
                {
                    Console.WriteLine($"    内容: {message.Content}");
                }
            }
            else
            {
                // 处理原始文本数据
                var text = Encoding.UTF8.GetString(e.Data);
                _logger?.LogInformation("收到文本数据 ({Length} 字节): {Text}", e.Data.Length, text);
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到文本 ({e.Data.Length} 字节): {text}");
            }

            _performanceMonitor?.RecordOperation(true, 0, e.Data.Length);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理接收数据时发生错误");
            _performanceMonitor?.RecordOperation(false);
        }
    }

    /// <summary>
    /// 数据发送事件处理
    /// </summary>
    private static void OnDataSent(object? sender, Liam.TcpClient.Events.DataSentEventArgs e)
    {
        _performanceMonitor?.RecordOperation(true, 0, e.Data.Length);
    }

    /// <summary>
    /// 消息接收事件处理
    /// </summary>
    private static void OnMessageReceived(object? sender, Liam.TcpClient.Events.MessageReceivedEventArgs e)
    {
        _logger?.LogDebug("收到消息: {MessageType}", e.Message.Type);
    }

    /// <summary>
    /// 消息发送事件处理
    /// </summary>
    private static void OnMessageSent(object? sender, Liam.TcpClient.Events.MessageSentEventArgs e)
    {
        _logger?.LogDebug("发送消息: {MessageType}", e.Message.Type);
    }

    /// <summary>
    /// 心跳事件处理
    /// </summary>
    private static void OnHeartbeat(object? sender, Liam.TcpClient.Events.HeartbeatEventArgs e)
    {
        var message = $"心跳{(e.IsRequest ? "请求" : "响应")}: 延迟 {e.Latency?.TotalMilliseconds:F2} ms";
        _logger?.LogDebug(message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    }

    /// <summary>
    /// 错误事件处理
    /// </summary>
    private static void OnError(object? sender, Liam.TcpClient.Events.TcpClientErrorEventArgs e)
    {
        var message = $"客户端错误: {e.Exception.Message}";
        _logger?.LogError(e.Exception, message);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
        _performanceMonitor?.RecordOperation(false);
    }

    /// <summary>
    /// 显示客户端信息
    /// </summary>
    private static void DisplayClientInfo()
    {
        if (_tcpClient == null) return;

        Console.WriteLine($"客户端配置:");
        Console.WriteLine($"  客户端ID: {_clientId}");
        Console.WriteLine($"  服务器地址: {_tcpClient.Configuration.ServerHost}:{_tcpClient.Configuration.ServerPort}");
        Console.WriteLine($"  自动重连: {(_tcpClient.Configuration.EnableAutoReconnect ? "启用" : "禁用")}");
        Console.WriteLine($"  心跳检测: {(_tcpClient.Configuration.EnableHeartbeat ? "启用" : "禁用")}");
        Console.WriteLine($"  SSL/TLS: {(_tcpClient.Configuration.EnableSsl ? "启用" : "禁用")}");
        Console.WriteLine($"  缓冲区大小: {_tcpClient.Configuration.BufferSize} 字节");
        Console.WriteLine($"  连接超时: {_tcpClient.Configuration.ConnectionTimeoutSeconds} 秒");
        Console.WriteLine();
    }

    /// <summary>
    /// 显示可用命令
    /// </summary>
    private static void DisplayCommands()
    {
        Console.WriteLine("=== 客户端控制台命令 ===");
        Console.WriteLine("  /connect       - 连接到服务器");
        Console.WriteLine("  /disconnect    - 断开连接");
        Console.WriteLine("  /reconnect     - 重新连接");
        Console.WriteLine("  /send <消息>   - 发送文本消息");
        Console.WriteLine("  /command <命令> - 发送命令消息");
        Console.WriteLine("  /ping          - 测试连接延迟");
        Console.WriteLine("  /status        - 显示连接状态");
        Console.WriteLine("  /stats         - 显示统计信息");
        Console.WriteLine("  /test <场景>   - 运行测试场景");
        Console.WriteLine("  /monitor       - 显示实时监控信息");
        Console.WriteLine("  /clear         - 清屏");
        Console.WriteLine("  /help          - 显示帮助信息");
        Console.WriteLine("  /quit          - 退出客户端");
        Console.WriteLine();
        Console.WriteLine("输入命令开始使用客户端...");
        Console.WriteLine();
    }

    /// <summary>
    /// 处理控制台输入
    /// </summary>
    private static async Task HandleConsoleInput()
    {
        while (true)
        {
            Console.Write("> ");
            var input = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(input))
                continue;

            var parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                continue;

            var command = parts[0].ToLower();

            try
            {
                switch (command)
                {
                    case "/connect":
                        await ConnectToServer();
                        break;

                    case "/disconnect":
                        await DisconnectFromServer();
                        break;

                    case "/reconnect":
                        await ReconnectToServer();
                        break;

                    case "/send":
                        if (parts.Length > 1)
                        {
                            var message = string.Join(" ", parts.Skip(1));
                            await SendTextMessage(message);
                        }
                        else
                        {
                            Console.WriteLine("用法: /send <消息>");
                        }
                        break;

                    case "/command":
                        if (parts.Length > 1)
                        {
                            var cmd = string.Join(" ", parts.Skip(1));
                            await SendCommandMessage(cmd);
                        }
                        else
                        {
                            Console.WriteLine("用法: /command <命令>");
                        }
                        break;

                    case "/ping":
                        await PingServer();
                        break;

                    case "/status":
                        DisplayConnectionStatus();
                        break;

                    case "/stats":
                        DisplayStatistics();
                        break;

                    case "/test":
                        if (parts.Length > 1)
                        {
                            await RunTestScenario(parts[1]);
                        }
                        else
                        {
                            DisplayAvailableTests();
                        }
                        break;

                    case "/monitor":
                        DisplayMonitorInfo();
                        break;

                    case "/clear":
                        Console.Clear();
                        DisplayClientInfo();
                        DisplayCommands();
                        break;

                    case "/help":
                        DisplayCommands();
                        break;

                    case "/quit":
                        Console.WriteLine("正在退出客户端...");
                        return;

                    default:
                        // 如果不是命令，直接作为文本消息发送
                        if (_tcpClient?.IsConnected == true)
                        {
                            await SendTextMessage(input);
                        }
                        else
                        {
                            Console.WriteLine($"未知命令: {command}，输入 /help 查看可用命令");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"执行命令时发生错误: {ex.Message}");
                _logger?.LogError(ex, "执行控制台命令时发生错误: {Command}", command);
            }
        }
    }

    /// <summary>
    /// 连接到服务器
    /// </summary>
    private static async Task ConnectToServer()
    {
        if (_tcpClient == null)
        {
            Console.WriteLine("客户端未初始化");
            return;
        }

        if (_tcpClient.IsConnected)
        {
            Console.WriteLine("已经连接到服务器");
            return;
        }

        Console.WriteLine("正在连接到服务器...");
        var success = await _tcpClient.ConnectAsync();

        if (success)
        {
            Console.WriteLine("连接成功！");
        }
        else
        {
            Console.WriteLine("连接失败");
        }
    }

    /// <summary>
    /// 断开服务器连接
    /// </summary>
    private static async Task DisconnectFromServer()
    {
        if (_tcpClient == null)
        {
            Console.WriteLine("客户端未初始化");
            return;
        }

        if (!_tcpClient.IsConnected)
        {
            Console.WriteLine("未连接到服务器");
            return;
        }

        Console.WriteLine("正在断开连接...");
        await _tcpClient.DisconnectAsync("用户主动断开");
        Console.WriteLine("已断开连接");
    }

    /// <summary>
    /// 重新连接到服务器
    /// </summary>
    private static async Task ReconnectToServer()
    {
        if (_tcpClient == null)
        {
            Console.WriteLine("客户端未初始化");
            return;
        }

        Console.WriteLine("正在重新连接...");
        var success = await _tcpClient.ReconnectAsync();

        if (success)
        {
            Console.WriteLine("重连成功！");
        }
        else
        {
            Console.WriteLine("重连失败");
        }
    }

    /// <summary>
    /// 发送文本消息
    /// </summary>
    private static async Task SendTextMessage(string text)
    {
        if (_tcpClient == null || !_tcpClient.IsConnected)
        {
            Console.WriteLine("未连接到服务器");
            return;
        }

        var message = DemoMessage.CreateTextMessage(text, _clientId);
        var success = await _tcpClient.SendTextAsync(message.ToJson());

        if (success)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 已发送: {text}");
        }
        else
        {
            Console.WriteLine("发送失败");
        }
    }

    /// <summary>
    /// 发送命令消息
    /// </summary>
    private static async Task SendCommandMessage(string command)
    {
        if (_tcpClient == null || !_tcpClient.IsConnected)
        {
            Console.WriteLine("未连接到服务器");
            return;
        }

        var message = DemoMessage.CreateCommandMessage(command, _clientId);
        var success = await _tcpClient.SendTextAsync(message.ToJson());

        if (success)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 已发送命令: {command}");
        }
        else
        {
            Console.WriteLine("发送命令失败");
        }
    }

    /// <summary>
    /// 测试服务器延迟
    /// </summary>
    private static async Task PingServer()
    {
        if (_tcpClient == null || !_tcpClient.IsConnected)
        {
            Console.WriteLine("未连接到服务器");
            return;
        }

        Console.WriteLine("正在测试连接延迟...");
        var latency = await _tcpClient.PingAsync();

        if (latency.HasValue)
        {
            Console.WriteLine($"延迟: {latency.Value:F2} ms");
        }
        else
        {
            Console.WriteLine("延迟测试失败");
        }
    }

    /// <summary>
    /// 显示连接状态
    /// </summary>
    private static void DisplayConnectionStatus()
    {
        if (_tcpClient == null)
        {
            Console.WriteLine("客户端未初始化");
            return;
        }

        Console.WriteLine("\n=== 连接状态 ===");
        Console.WriteLine($"连接状态: {(_tcpClient.IsConnected ? "已连接" : "未连接")}");
        Console.WriteLine($"正在连接: {(_tcpClient.IsConnecting ? "是" : "否")}");
        Console.WriteLine($"正在重连: {(_tcpClient.IsReconnecting ? "是" : "否")}");
        Console.WriteLine($"客户端状态: {_tcpClient.Status}");

        if (_tcpClient.ConnectedAt.HasValue)
        {
            Console.WriteLine($"连接时间: {_tcpClient.ConnectedAt.Value:yyyy-MM-dd HH:mm:ss}");
        }

        if (_tcpClient.ConnectionDuration.HasValue)
        {
            Console.WriteLine($"连接持续时间: {_tcpClient.ConnectionDuration.Value:hh\\:mm\\:ss}");
        }

        if (_tcpClient.ConnectionInfo != null)
        {
            Console.WriteLine($"服务器地址: {_tcpClient.ConnectionInfo.ServerHost}:{_tcpClient.ConnectionInfo.ServerPort}");
            Console.WriteLine($"本地地址: {_tcpClient.ConnectionInfo.LocalEndPoint}");
            Console.WriteLine($"连接质量: {_tcpClient.GetConnectionQuality():F1}%");
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 显示统计信息
    /// </summary>
    private static void DisplayStatistics()
    {
        if (_tcpClient == null)
        {
            Console.WriteLine("客户端未初始化");
            return;
        }

        var stats = _tcpClient.GetStatistics();
        Console.WriteLine("\n=== 客户端统计信息 ===");
        Console.WriteLine($"总连接次数: {stats.TotalConnections}");
        Console.WriteLine($"成功连接次数: {stats.SuccessfulConnections}");
        Console.WriteLine($"失败连接次数: {stats.FailedConnections}");
        Console.WriteLine($"重连次数: {stats.ReconnectAttempts}");
        Console.WriteLine($"总发送字节数: {stats.TotalBytesSent:N0}");
        Console.WriteLine($"总接收字节数: {stats.TotalBytesReceived:N0}");
        Console.WriteLine($"总发送消息数: {stats.TotalMessagesSent:N0}");
        Console.WriteLine($"总接收消息数: {stats.TotalMessagesReceived:N0}");
        Console.WriteLine($"总错误数: {stats.TotalErrors:N0}");
        Console.WriteLine($"平均延迟: {stats.AverageLatency?.TotalMilliseconds:F2} ms");
        Console.WriteLine($"最后心跳时间: {stats.LastHeartbeatTime:yyyy-MM-dd HH:mm:ss}");

        if (_performanceMonitor != null)
        {
            Console.WriteLine("\n=== 性能监控信息 ===");
            Console.WriteLine(_performanceMonitor.GetRealTimeStats());
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 显示可用测试
    /// </summary>
    private static void DisplayAvailableTests()
    {
        Console.WriteLine("\n=== 可用测试场景 ===");
        Console.WriteLine("  basic       - 基本连接测试");
        Console.WriteLine("  message     - 消息交换测试");
        Console.WriteLine("  heartbeat   - 心跳检测测试");
        Console.WriteLine("  performance - 性能压力测试");
        Console.WriteLine("  large       - 大数据传输测试");
        Console.WriteLine("  reconnect   - 自动重连测试");
        Console.WriteLine("\n用法: /test <场景名称>");
    }

    /// <summary>
    /// 运行测试场景
    /// </summary>
    private static async Task RunTestScenario(string scenarioName)
    {
        TestScenario? scenario = scenarioName.ToLower() switch
        {
            "basic" => TestScenario.CreateBasicConnectionTest(),
            "message" => TestScenario.CreateMessageExchangeTest(),
            "heartbeat" => TestScenario.CreateHeartbeatTest(),
            "performance" => TestScenario.CreatePerformanceStressTest(),
            "large" => TestScenario.CreateLargeDataTransferTest(),
            "reconnect" => TestScenario.CreateAutoReconnectTest(),
            _ => null
        };

        if (scenario == null)
        {
            Console.WriteLine($"未知测试场景: {scenarioName}");
            DisplayAvailableTests();
            return;
        }

        Console.WriteLine($"正在运行测试场景: {scenario.Name}");
        Console.WriteLine($"描述: {scenario.Description}");

        _activeScenarios[scenario.Id] = scenario;

        var testMonitor = new PerformanceMonitor();
        testMonitor.Start();

        try
        {
            await ExecuteTestScenario(scenario, testMonitor);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试执行失败: {ex.Message}");
            _logger?.LogError(ex, "测试场景执行失败: {ScenarioName}", scenario.Name);
        }
        finally
        {
            testMonitor.Stop();
            var result = testMonitor.GenerateResult(scenario.Id, scenario.Name);
            _testResults[result.Id] = result;

            Console.WriteLine("\n=== 测试结果 ===");
            Console.WriteLine(result.GetSummary());
            testMonitor.Dispose();
        }
    }

    /// <summary>
    /// 执行测试场景
    /// </summary>
    private static async Task ExecuteTestScenario(TestScenario scenario, PerformanceMonitor monitor)
    {
        switch (scenario.Type)
        {
            case TestScenarioType.BasicConnection:
                await ExecuteBasicConnectionTest(scenario, monitor);
                break;
            case TestScenarioType.MessageExchange:
                await ExecuteMessageExchangeTest(scenario, monitor);
                break;
            case TestScenarioType.PerformanceStressTest:
                await ExecutePerformanceStressTest(scenario, monitor);
                break;
            case TestScenarioType.LargeDataTransferTest:
                await ExecuteLargeDataTransferTest(scenario, monitor);
                break;
            default:
                Console.WriteLine("测试场景暂未实现");
                break;
        }
    }

    /// <summary>
    /// 执行基本连接测试
    /// </summary>
    private static async Task ExecuteBasicConnectionTest(TestScenario scenario, PerformanceMonitor monitor)
    {
        if (_tcpClient == null) return;

        // 如果未连接，先连接
        if (!_tcpClient.IsConnected)
        {
            var connected = await _tcpClient.ConnectAsync();
            monitor.RecordOperation(connected);

            if (!connected)
            {
                Console.WriteLine("连接失败，测试终止");
                return;
            }
        }

        // 发送测试消息
        var testMessage = DemoMessage.CreateTextMessage("Basic connection test", _clientId);
        var sent = await _tcpClient.SendTextAsync(testMessage.ToJson());
        monitor.RecordOperation(sent, 0, testMessage.GetSize());

        Console.WriteLine("基本连接测试完成");
    }

    /// <summary>
    /// 显示监控信息
    /// </summary>
    private static void DisplayMonitorInfo()
    {
        if (_performanceMonitor == null)
        {
            Console.WriteLine("性能监控器未初始化");
            return;
        }

        Console.WriteLine("\n=== 实时监控信息 ===");
        Console.WriteLine(_performanceMonitor.GetRealTimeStats());
        Console.WriteLine($"内存使用: {_performanceMonitor.GetCurrentMemoryUsage() / 1024.0 / 1024.0:F2} MB");
        Console.WriteLine($"CPU使用率: {_performanceMonitor.GetCpuUsage():F1}%");
        Console.WriteLine();
    }

    /// <summary>
    /// 执行消息交换测试
    /// </summary>
    private static async Task ExecuteMessageExchangeTest(TestScenario scenario, PerformanceMonitor monitor)
    {
        if (_tcpClient == null || !_tcpClient.IsConnected) return;

        Console.WriteLine($"开始消息交换测试，发送 {scenario.MessageCount} 条消息...");

        for (int i = 0; i < scenario.MessageCount; i++)
        {
            var message = DataGenerator.GeneratePerformanceTestMessage(i + 1, scenario.MessageSize, _clientId);
            var startTime = DateTime.UtcNow;

            var sent = await _tcpClient.SendTextAsync(message.ToJson());
            var latency = (DateTime.UtcNow - startTime).TotalMilliseconds;

            monitor.RecordOperation(sent, latency, message.GetSize());

            if (scenario.SendIntervalMs > 0)
            {
                await Task.Delay(scenario.SendIntervalMs);
            }

            if ((i + 1) % 10 == 0)
            {
                Console.WriteLine($"已发送 {i + 1}/{scenario.MessageCount} 条消息");
            }
        }

        Console.WriteLine("消息交换测试完成");
    }

    /// <summary>
    /// 执行性能压力测试
    /// </summary>
    private static async Task ExecutePerformanceStressTest(TestScenario scenario, PerformanceMonitor monitor)
    {
        if (_tcpClient == null || !_tcpClient.IsConnected) return;

        Console.WriteLine($"开始性能压力测试，持续 {scenario.DurationSeconds} 秒...");
        var endTime = DateTime.UtcNow.AddSeconds(scenario.DurationSeconds);
        int messageCount = 0;

        while (DateTime.UtcNow < endTime)
        {
            var message = DataGenerator.GeneratePerformanceTestMessage(++messageCount, scenario.MessageSize, _clientId);
            var startTime = DateTime.UtcNow;

            var sent = await _tcpClient.SendTextAsync(message.ToJson());
            var latency = (DateTime.UtcNow - startTime).TotalMilliseconds;

            monitor.RecordOperation(sent, latency, message.GetSize());

            if (scenario.SendIntervalMs > 0)
            {
                await Task.Delay(scenario.SendIntervalMs);
            }

            if (messageCount % 100 == 0)
            {
                Console.WriteLine($"已发送 {messageCount} 条消息，剩余时间: {(endTime - DateTime.UtcNow).TotalSeconds:F1} 秒");
            }
        }

        Console.WriteLine($"性能压力测试完成，共发送 {messageCount} 条消息");
    }

    /// <summary>
    /// 执行大数据传输测试
    /// </summary>
    private static async Task ExecuteLargeDataTransferTest(TestScenario scenario, PerformanceMonitor monitor)
    {
        if (_tcpClient == null || !_tcpClient.IsConnected) return;

        Console.WriteLine($"开始大数据传输测试，每条消息 {scenario.MessageSize / 1024.0 / 1024.0:F2} MB...");

        for (int i = 0; i < scenario.MessageCount; i++)
        {
            var message = DataGenerator.GenerateLargeDataMessage(scenario.MessageSize, _clientId);
            var startTime = DateTime.UtcNow;

            var sent = await _tcpClient.SendTextAsync(message.ToJson());
            var latency = (DateTime.UtcNow - startTime).TotalMilliseconds;

            monitor.RecordOperation(sent, latency, message.GetSize());

            Console.WriteLine($"已传输 {i + 1}/{scenario.MessageCount} 个大数据包，" +
                            $"大小: {message.GetSize() / 1024.0 / 1024.0:F2} MB，" +
                            $"耗时: {latency:F2} ms");

            // 大数据传输间隔稍长一些
            await Task.Delay(1000);
        }

        Console.WriteLine("大数据传输测试完成");
    }

    /// <summary>
    /// 客户端配置类
    /// </summary>
    private class ClientConfig
    {
        public string ServerHost { get; set; } = "localhost";
        public int ServerPort { get; set; } = 8888;
        public bool EnableAutoReconnect { get; set; } = false;
        public int ReconnectInterval { get; set; } = 5;
        public bool EnableHeartbeat { get; set; } = false;
        public int HeartbeatInterval { get; set; } = 30;
        public int HeartbeatTimeout { get; set; } = 60;
        public bool EnableSsl { get; set; } = false;
        public int BufferSize { get; set; } = 4096;
        public int ConnectionTimeout { get; set; } = 30;
    }
}
