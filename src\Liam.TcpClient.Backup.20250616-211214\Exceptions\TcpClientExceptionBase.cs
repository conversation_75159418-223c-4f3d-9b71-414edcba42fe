using System.Text.Json;
using Liam.TcpClient.Models;
using Liam.TcpClient.Performance;

namespace Liam.TcpClient.Exceptions;

/// <summary>
/// TCP客户端异常基类，提供详细的上下文信息
/// </summary>
public abstract class TcpClientExceptionBase : Exception
{
    /// <summary>
    /// 异常发生时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 连接状态信息
    /// </summary>
    public string? ConnectionState { get; }

    /// <summary>
    /// 客户端配置信息（敏感信息已脱敏）
    /// </summary>
    public string? ConfigurationInfo { get; }

    /// <summary>
    /// 操作上下文信息
    /// </summary>
    public Dictionary<string, object> OperationContext { get; }

    /// <summary>
    /// 异常分类
    /// </summary>
    public abstract TcpClientExceptionCategory Category { get; }

    /// <summary>
    /// 异常严重程度
    /// </summary>
    public virtual TcpClientExceptionSeverity Severity => TcpClientExceptionSeverity.Error;

    /// <summary>
    /// 是否可重试
    /// </summary>
    public virtual bool IsRetryable => false;

    /// <summary>
    /// 建议的重试延迟时间
    /// </summary>
    public virtual TimeSpan? SuggestedRetryDelay => null;

    /// <summary>
    /// 初始化TCP客户端异常基类
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="connectionState">连接状态</param>
    /// <param name="configuration">客户端配置</param>
    /// <param name="operationContext">操作上下文</param>
    protected TcpClientExceptionBase(
        string message, 
        Exception? innerException = null,
        string? connectionState = null,
        TcpClientConfig? configuration = null,
        Dictionary<string, object>? operationContext = null) 
        : base(message, innerException)
    {
        Timestamp = DateTime.UtcNow;
        ConnectionState = connectionState;
        ConfigurationInfo = CreateSafeConfigurationInfo(configuration);
        OperationContext = operationContext ?? new Dictionary<string, object>();
        
        // 添加基础上下文信息
        OperationContext["ExceptionType"] = GetType().Name;
        OperationContext["Timestamp"] = Timestamp;
        OperationContext["ThreadId"] = Environment.CurrentManagedThreadId;
        
        if (!string.IsNullOrEmpty(connectionState))
        {
            OperationContext["ConnectionState"] = connectionState;
        }
    }

    /// <summary>
    /// 创建安全的配置信息（脱敏处理）
    /// </summary>
    /// <param name="config">客户端配置</param>
    /// <returns>脱敏后的配置信息</returns>
    private static string? CreateSafeConfigurationInfo(TcpClientConfig? config)
    {
        if (config == null) return null;

        try
        {
            var safeConfig = new
            {
                Host = MaskSensitiveInfo(config.Host),
                Port = config.Port,
                EnableSsl = config.EnableSsl,
                EnableHeartbeat = config.EnableHeartbeat,
                EnableAutoReconnect = config.EnableAutoReconnect,
                ConnectionTimeoutSeconds = config.ConnectionTimeoutSeconds,
                MaxMessageLength = config.MaxMessageLength,
                ClientId = MaskSensitiveInfo(config.ClientId),
                ClientName = MaskSensitiveInfo(config.ClientName)
            };

            return JsonSerializer.Serialize(safeConfig, new JsonSerializerOptions 
            { 
                WriteIndented = false 
            });
        }
        catch
        {
            return "配置信息序列化失败";
        }
    }

    /// <summary>
    /// 脱敏处理敏感信息
    /// </summary>
    /// <param name="value">原始值</param>
    /// <returns>脱敏后的值</returns>
    private static string? MaskSensitiveInfo(string? value)
    {
        if (string.IsNullOrEmpty(value)) return value;
        if (value.Length <= 4) return "****";
        
        return value[..2] + "****" + value[^2..];
    }

    /// <summary>
    /// 添加操作上下文信息
    /// </summary>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    public void AddContext(string key, object value)
    {
        ArgumentNullException.ThrowIfNull(key);
        OperationContext[key] = value;
    }

    /// <summary>
    /// 获取详细的异常信息
    /// </summary>
    /// <returns>详细异常信息</returns>
    public virtual string GetDetailedMessage()
    {
        return StringOptimizer.BuildString(sb =>
        {
            sb.Append("异常类型: ").AppendLine(Category.ToString());
            sb.Append("严重程度: ").AppendLine(Severity.ToString());
            sb.Append("发生时间: ").Append(Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff")).AppendLine(" UTC");
            sb.Append("消息: ").AppendLine(Message);

            if (!string.IsNullOrEmpty(ConnectionState))
            {
                sb.Append("连接状态: ").AppendLine(ConnectionState);
            }

            if (!string.IsNullOrEmpty(ConfigurationInfo))
            {
                sb.Append("配置信息: ").AppendLine(ConfigurationInfo);
            }

            if (IsRetryable)
            {
                sb.AppendLine("可重试: 是");
                if (SuggestedRetryDelay.HasValue)
                {
                    sb.Append("建议重试延迟: ").Append(SuggestedRetryDelay.Value.TotalSeconds).AppendLine("秒");
                }
            }
            else
            {
                sb.AppendLine("可重试: 否");
            }

            if (OperationContext.Count > 0)
            {
                sb.AppendLine("操作上下文:");
                foreach (var kvp in OperationContext)
                {
                    sb.Append("  ").Append(kvp.Key).Append(": ").AppendLine(kvp.Value?.ToString());
                }
            }

            if (InnerException != null)
            {
                sb.Append("内部异常: ").Append(InnerException.GetType().Name).Append(": ").AppendLine(InnerException.Message);
            }
        });
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>异常的字符串表示</returns>
    public override string ToString()
    {
        return GetDetailedMessage() + Environment.NewLine + base.ToString();
    }
}

/// <summary>
/// TCP客户端异常分类
/// </summary>
public enum TcpClientExceptionCategory
{
    /// <summary>
    /// 网络连接异常
    /// </summary>
    Network,

    /// <summary>
    /// 配置异常
    /// </summary>
    Configuration,

    /// <summary>
    /// 验证异常
    /// </summary>
    Validation,

    /// <summary>
    /// 安全异常
    /// </summary>
    Security,

    /// <summary>
    /// 协议异常
    /// </summary>
    Protocol,

    /// <summary>
    /// 超时异常
    /// </summary>
    Timeout,

    /// <summary>
    /// 认证异常
    /// </summary>
    Authentication,

    /// <summary>
    /// 资源异常
    /// </summary>
    Resource
}

/// <summary>
/// TCP客户端异常严重程度
/// </summary>
public enum TcpClientExceptionSeverity
{
    /// <summary>
    /// 信息级别
    /// </summary>
    Information,

    /// <summary>
    /// 警告级别
    /// </summary>
    Warning,

    /// <summary>
    /// 错误级别
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误级别
    /// </summary>
    Critical,

    /// <summary>
    /// 致命错误级别
    /// </summary>
    Fatal
}
