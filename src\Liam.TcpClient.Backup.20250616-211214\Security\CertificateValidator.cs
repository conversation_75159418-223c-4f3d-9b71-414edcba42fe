using System.Net.Security;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Extensions.Logging;
using Liam.TcpClient.Models;

namespace Liam.TcpClient.Security;

/// <summary>
/// SSL证书验证器
/// </summary>
public class CertificateValidator
{
    private readonly ILogger<CertificateValidator>? _logger;
    private readonly SslConfig _sslConfig;

    /// <summary>
    /// 初始化证书验证器
    /// </summary>
    /// <param name="sslConfig">SSL配置</param>
    /// <param name="logger">日志记录器</param>
    public CertificateValidator(SslConfig sslConfig, ILogger<CertificateValidator>? logger = null)
    {
        _sslConfig = sslConfig ?? throw new ArgumentNullException(nameof(sslConfig));
        _logger = logger;
    }

    /// <summary>
    /// 验证远程证书
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="certificate">证书</param>
    /// <param name="chain">证书链</param>
    /// <param name="sslPolicyErrors">SSL策略错误</param>
    /// <returns>验证结果</returns>
    public bool ValidateRemoteCertificate(object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors)
    {
        if (certificate == null)
        {
            _logger?.LogError("SSL证书验证失败: 证书为空");
            return false;
        }

        try
        {
            var cert2 = new X509Certificate2(certificate);
            var validationContext = new CertificateValidationContext
            {
                Certificate = cert2,
                Chain = chain,
                SslPolicyErrors = sslPolicyErrors,
                ValidationMode = _sslConfig.ValidationMode
            };

            return _sslConfig.ValidationMode switch
            {
                CertificateValidationMode.Strict => ValidateStrict(validationContext),
                CertificateValidationMode.Development => ValidateDevelopment(validationContext),
                CertificateValidationMode.Custom => ValidateCustom(validationContext),
                CertificateValidationMode.Thumbprint => ValidateThumbprint(validationContext),
                _ => ValidateStrict(validationContext)
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "SSL证书验证过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 严格模式验证
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <returns>验证结果</returns>
    private bool ValidateStrict(CertificateValidationContext context)
    {
        if (context.SslPolicyErrors == SslPolicyErrors.None)
        {
            _logger?.LogDebug("SSL证书验证通过 - 严格模式");
            return true;
        }

        _logger?.LogWarning("SSL证书验证失败 - 严格模式: {Errors}", context.SslPolicyErrors);
        LogCertificateDetails(context.Certificate, "严格模式验证失败");
        return false;
    }

    /// <summary>
    /// 开发模式验证
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <returns>验证结果</returns>
    private bool ValidateDevelopment(CertificateValidationContext context)
    {
        if (context.SslPolicyErrors == SslPolicyErrors.None)
        {
            _logger?.LogDebug("SSL证书验证通过 - 开发模式");
            return true;
        }

        // 开发模式下允许的错误类型
        var allowedErrors = SslPolicyErrors.None;
        
        if (_sslConfig.AllowSelfSignedCertificates)
        {
            allowedErrors |= SslPolicyErrors.RemoteCertificateChainErrors;
        }
        
        if (_sslConfig.AllowNameMismatch)
        {
            allowedErrors |= SslPolicyErrors.RemoteCertificateNameMismatch;
        }

        var hasUnallowedErrors = (context.SslPolicyErrors & ~allowedErrors) != SslPolicyErrors.None;
        
        if (!hasUnallowedErrors)
        {
            _logger?.LogWarning("SSL证书验证通过 - 开发模式 (存在被允许的错误): {Errors}", context.SslPolicyErrors);
            return true;
        }

        _logger?.LogError("SSL证书验证失败 - 开发模式: {Errors}", context.SslPolicyErrors);
        LogCertificateDetails(context.Certificate, "开发模式验证失败");
        return false;
    }

    /// <summary>
    /// 自定义模式验证
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <returns>验证结果</returns>
    private bool ValidateCustom(CertificateValidationContext context)
    {
        if (_sslConfig.RemoteCertificateValidationCallback == null)
        {
            _logger?.LogWarning("自定义验证模式但未提供验证回调，回退到严格模式");
            return ValidateStrict(context);
        }

        try
        {
            var result = _sslConfig.RemoteCertificateValidationCallback(
                context, context.Certificate, context.Chain, context.SslPolicyErrors);

            _logger?.LogDebug("SSL证书自定义验证结果: {Result}", result);

            if (!result)
            {
                LogCertificateDetails(context.Certificate, "自定义验证失败");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "自定义证书验证回调执行失败");
            return false;
        }
    }

    /// <summary>
    /// 指纹验证模式
    /// </summary>
    /// <param name="context">验证上下文</param>
    /// <returns>验证结果</returns>
    private bool ValidateThumbprint(CertificateValidationContext context)
    {
        if (_sslConfig.TrustedCertificateThumbprints.Count == 0)
        {
            _logger?.LogWarning("指纹验证模式但未配置受信任的证书指纹，回退到严格模式");
            return ValidateStrict(context);
        }

        var certificateThumbprint = GetCertificateThumbprint(context.Certificate);
        var isThumbprintTrusted = _sslConfig.TrustedCertificateThumbprints.Contains(certificateThumbprint);

        if (isThumbprintTrusted)
        {
            _logger?.LogDebug("SSL证书指纹验证通过: {Thumbprint}", certificateThumbprint);
            return true;
        }

        _logger?.LogWarning("SSL证书指纹验证失败: {Thumbprint}, 受信任指纹: {TrustedThumbprints}", 
            certificateThumbprint, string.Join(", ", _sslConfig.TrustedCertificateThumbprints));
        LogCertificateDetails(context.Certificate, "指纹验证失败");
        return false;
    }

    /// <summary>
    /// 获取证书SHA-256指纹
    /// </summary>
    /// <param name="certificate">证书</param>
    /// <returns>指纹</returns>
    private static string GetCertificateThumbprint(X509Certificate2 certificate)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return Convert.ToHexString(hash);
    }

    /// <summary>
    /// 记录证书详细信息
    /// </summary>
    /// <param name="certificate">证书</param>
    /// <param name="context">上下文</param>
    private void LogCertificateDetails(X509Certificate2 certificate, string context)
    {
        if (_logger?.IsEnabled(LogLevel.Warning) != true) return;

        _logger.LogWarning("""
            {Context} - 证书详细信息:
            主题: {Subject}
            颁发者: {Issuer}
            序列号: {SerialNumber}
            指纹(SHA-256): {Thumbprint}
            有效期: {NotBefore} - {NotAfter}
            是否有效: {IsValid}
            """,
            context,
            certificate.Subject,
            certificate.Issuer,
            certificate.SerialNumber,
            GetCertificateThumbprint(certificate),
            certificate.NotBefore,
            certificate.NotAfter,
            certificate.NotBefore <= DateTime.Now && certificate.NotAfter >= DateTime.Now);
    }
}

/// <summary>
/// 证书验证上下文
/// </summary>
public class CertificateValidationContext
{
    /// <summary>
    /// 证书
    /// </summary>
    public required X509Certificate2 Certificate { get; init; }

    /// <summary>
    /// 证书链
    /// </summary>
    public X509Chain? Chain { get; init; }

    /// <summary>
    /// SSL策略错误
    /// </summary>
    public SslPolicyErrors SslPolicyErrors { get; init; }

    /// <summary>
    /// 验证模式
    /// </summary>
    public CertificateValidationMode ValidationMode { get; init; }
}
