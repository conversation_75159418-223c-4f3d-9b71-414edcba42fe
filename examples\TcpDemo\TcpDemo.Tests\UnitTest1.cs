using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Xunit;
using FluentAssertions;
using Moq;
using Liam.TcpServer.Extensions;
using Liam.TcpServer.Interfaces;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.Logging.Extensions;
using TcpDemo.Shared.Models;
using TcpDemo.Shared.Utils;
using System.Text;

namespace TcpDemo.Tests;

/// <summary>
/// TCP通信集成测试
/// </summary>
public class TcpCommunicationIntegrationTests : IDisposable
{
    private readonly IHost _serverHost;
    private readonly IHost _clientHost;
    private readonly ITcpServer _tcpServer;
    private readonly ITcpClient _tcpClient;
    private readonly ILogger<TcpCommunicationIntegrationTests> _logger;
    private bool _disposed;

    public TcpCommunicationIntegrationTests()
    {
        // 创建服务器主机
        var serverBuilder = Host.CreateDefaultBuilder();
        serverBuilder.ConfigureServices((context, services) =>
        {
            services.AddLiamLogging(options =>
            {
                options.MinimumLevel = LogLevel.Debug;
                options.EnableConsoleLogging = false;
                options.EnableFileLogging = false;
            });

            services.AddTcpServer(config =>
            {
                config.Port = 8889; // 使用不同端口避免冲突
                config.MaxConnections = 10;
                config.EnableHeartbeat = false;
                config.BufferSize = 4096;
            });
        });
        _serverHost = serverBuilder.Build();
        _tcpServer = _serverHost.Services.GetRequiredService<ITcpServer>();

        // 创建客户端主机
        var clientBuilder = Host.CreateDefaultBuilder();
        clientBuilder.ConfigureServices((context, services) =>
        {
            services.AddLiamLogging(options =>
            {
                options.MinimumLevel = LogLevel.Debug;
                options.EnableConsoleLogging = false;
                options.EnableFileLogging = false;
            });

            services.AddTcpClient(config =>
            {
                config.ServerHost = "localhost";
                config.ServerPort = 8889;
                config.EnableAutoReconnect = false;
                config.EnableHeartbeat = false;
                config.BufferSize = 4096;
                config.ConnectionTimeoutSeconds = 10;
            });
        });
        _clientHost = clientBuilder.Build();
        _tcpClient = _clientHost.Services.GetRequiredService<ITcpClient>();
        _logger = _clientHost.Services.GetRequiredService<ILogger<TcpCommunicationIntegrationTests>>();
    }

    [Fact]
    public async Task BasicConnection_ShouldConnectAndDisconnectSuccessfully()
    {
        // Arrange
        await _tcpServer.StartAsync();

        // Act
        var connectResult = await _tcpClient.ConnectAsync();
        var isConnected = _tcpClient.IsConnected;
        await _tcpClient.DisconnectAsync();
        var isDisconnected = !_tcpClient.IsConnected;

        // Assert
        connectResult.Should().BeTrue();
        isConnected.Should().BeTrue();
        isDisconnected.Should().BeTrue();

        await _tcpServer.StopAsync();
    }

    [Fact]
    public async Task MessageExchange_ShouldSendAndReceiveMessages()
    {
        // Arrange
        await _tcpServer.StartAsync();
        await _tcpClient.ConnectAsync();

        var receivedMessages = new List<string>();
        _tcpClient.DataReceived += (sender, e) =>
        {
            var text = Encoding.UTF8.GetString(e.Data);
            receivedMessages.Add(text);
        };

        var testMessage = DemoMessage.CreateTextMessage("Test message", "TestClient");

        // Act
        var sendResult = await _tcpClient.SendTextAsync(testMessage.ToJson());

        // Wait for response
        await Task.Delay(1000);

        // Assert
        sendResult.Should().BeTrue();
        receivedMessages.Should().NotBeEmpty();

        await _tcpClient.DisconnectAsync();
        await _tcpServer.StopAsync();
    }

    [Fact]
    public async Task PerformanceTest_ShouldHandleMultipleMessages()
    {
        // Arrange
        await _tcpServer.StartAsync();
        await _tcpClient.ConnectAsync();

        var monitor = new PerformanceMonitor();
        monitor.Start();

        const int messageCount = 100;
        const int messageSize = 1024;

        // Act
        for (int i = 0; i < messageCount; i++)
        {
            var message = DataGenerator.GeneratePerformanceTestMessage(i + 1, messageSize, "TestClient");
            var startTime = DateTime.UtcNow;

            var sent = await _tcpClient.SendTextAsync(message.ToJson());
            var latency = (DateTime.UtcNow - startTime).TotalMilliseconds;

            monitor.RecordOperation(sent, latency, message.GetSize());
        }

        monitor.Stop();
        var result = monitor.GenerateResult("perf-test", "Performance Test");

        // Assert
        result.SuccessRate.Should().BeGreaterThan(0.95); // 95% success rate
        result.ThroughputOpsPerSec.Should().BeGreaterThan(10); // At least 10 ops/sec
        result.AverageLatencyMs.Should().BeLessThan(1000); // Less than 1 second average latency

        monitor.Dispose();
        await _tcpClient.DisconnectAsync();
        await _tcpServer.StopAsync();
    }

    [Fact]
    public async Task LargeDataTransfer_ShouldTransferDataSuccessfully()
    {
        // Arrange
        await _tcpServer.StartAsync();
        await _tcpClient.ConnectAsync();

        var receivedData = new List<byte[]>();
        _tcpClient.DataReceived += (sender, e) =>
        {
            receivedData.Add(e.Data);
        };

        const int dataSize = 1024 * 1024; // 1MB
        var largeMessage = DataGenerator.GenerateLargeDataMessage(dataSize, "TestClient");

        // Act
        var sendResult = await _tcpClient.SendTextAsync(largeMessage.ToJson());

        // Wait for data transfer
        await Task.Delay(5000);

        // Assert
        sendResult.Should().BeTrue();
        receivedData.Should().NotBeEmpty();

        await _tcpClient.DisconnectAsync();
        await _tcpServer.StopAsync();
    }

    [Fact]
    public async Task ConcurrentConnections_ShouldHandleMultipleClients()
    {
        // Arrange
        await _tcpServer.StartAsync();

        const int clientCount = 5;
        var clients = new List<ITcpClient>();
        var tasks = new List<Task>();

        // Create multiple clients
        for (int i = 0; i < clientCount; i++)
        {
            var clientBuilder = Host.CreateDefaultBuilder();
            clientBuilder.ConfigureServices((context, services) =>
            {
                services.AddLiamLogging(options =>
                {
                    options.MinimumLevel = LogLevel.Warning;
                    options.EnableConsoleLogging = false;
                    options.EnableFileLogging = false;
                });

                services.AddTcpClient(config =>
                {
                    config.ServerHost = "localhost";
                    config.ServerPort = 8889;
                    config.EnableAutoReconnect = false;
                    config.EnableHeartbeat = false;
                    config.ConnectionTimeoutSeconds = 10;
                });
            });

            var clientHost = clientBuilder.Build();
            var client = clientHost.Services.GetRequiredService<ITcpClient>();
            clients.Add(client);
        }

        // Act - Connect all clients concurrently
        foreach (var client in clients)
        {
            tasks.Add(client.ConnectAsync().AsTask());
        }

        var connectResults = await Task.WhenAll(tasks);

        // Send messages from each client
        tasks.Clear();
        foreach (var client in clients)
        {
            tasks.Add(SendTestMessage(client));
        }

        await Task.WhenAll(tasks);

        // Assert
        connectResults.Should().AllSatisfy(result => result.Should().BeTrue());
        _tcpServer.ConnectionCount.Should().Be(clientCount);

        // Cleanup
        foreach (var client in clients)
        {
            await client.DisconnectAsync();
        }

        await _tcpServer.StopAsync();
    }

    private async Task SendTestMessage(ITcpClient client)
    {
        var message = DemoMessage.CreateTextMessage($"Test from {client.GetHashCode()}", "TestClient");
        await client.SendTextAsync(message.ToJson());
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _serverHost?.Dispose();
            _clientHost?.Dispose();
            _disposed = true;
        }
    }
}